<?php
/**
 * mail_sender.php
 *
 * Bibliothèque pour l'envoi d'emails aux clients
 * Utilisée par le système de notifications pour les tickets Mikrotik
 * Support SMTP et fonction mail() native
 */

// Inclusion de la fonction de log
require_once __DIR__ . '/../utils/logger.php';

/**
 * Envoie un email à un destinataire
 *
 * @param string $to Email du destinataire
 * @param string $subject Sujet de l'email
 * @param string $message Contenu de l'email
 * @param array $options Options supplémentaires (cc, bcc, from, etc.)
 * @return bool True si l'email a été envoyé avec succès, False sinon
 */
function sendMail($to, $subject, $message, $options = []) {
    // Vérifier que l'adresse email est valide
    if (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
        logMessage("Adresse email invalide", ['to' => $to], 'error');
        return false;
    }

    // Charger la configuration email
    $configFile = __DIR__ . '/../config/email_config.php';
    $emailConfig = file_exists($configFile) ? require $configFile : [];

    // Vérifier si l'envoi d'email est activé
    if (isset($emailConfig['enabled']) && !$emailConfig['enabled']) {
        logMessage("Envoi d'email désactivé", ['to' => $to], 'info');
        return false;
    }

    try {
        // Paramètres de l'expéditeur
        $from = isset($options['from']) ? $options['from'] :
                (isset($emailConfig['from']['email']) ? $emailConfig['from']['email'] : '<EMAIL>');
        $fromName = isset($options['fromName']) ? $options['fromName'] :
                   (isset($emailConfig['from']['name']) ? $emailConfig['from']['name'] : 'Service WiFi Moulin');

        // Choisir la méthode d'envoi
        $method = isset($emailConfig['method']) ? $emailConfig['method'] : 'mail';

        if ($method === 'log') {
            return sendMailViaLog($to, $subject, $message, $options, $from, $fromName);
        } elseif ($method === 'smtp' && isset($emailConfig['smtp'])) {
            return sendMailViaSMTP($to, $subject, $message, $options, $emailConfig);
        } else {
            return sendMailViaPhpMail($to, $subject, $message, $options, $from, $fromName);
        }
    } catch (Exception $e) {
        logMessage("Exception lors de l'envoi d'email", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'to' => $to
        ], 'error');
        return false;
    }
}

/**
 * Envoie un email via la fonction mail() native de PHP
 */
function sendMailViaPhpMail($to, $subject, $message, $options, $from, $fromName) {
    // En-têtes de l'email
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/plain; charset=UTF-8',
        'From: ' . $fromName . ' <' . $from . '>',
        'Reply-To: ' . $from,
        'X-Mailer: PHP/' . phpversion()
    ];

    // Ajout de CC si spécifié
    if (isset($options['cc']) && is_array($options['cc'])) {
        foreach ($options['cc'] as $cc) {
            if (filter_var($cc, FILTER_VALIDATE_EMAIL)) {
                $headers[] = 'Cc: ' . $cc;
            }
        }
    }

    // Ajout de BCC si spécifié
    if (isset($options['bcc']) && is_array($options['bcc'])) {
        foreach ($options['bcc'] as $bcc) {
            if (filter_var($bcc, FILTER_VALIDATE_EMAIL)) {
                $headers[] = 'Bcc: ' . $bcc;
            }
        }
    }

    // Préparer le message (convertir en HTML si nécessaire)
    $finalMessage = $message;
    if (isset($options['html']) && $options['html'] === true) {
        $headers[1] = 'Content-type: text/html; charset=UTF-8';
        // Si le message n'est pas déjà en HTML, le convertir
        if (strpos($message, '<html>') === false) {
            $finalMessage = '<html><body>' . nl2br($message) . '</body></html>';
        }
    }

    // Envoi de l'email
    $result = mail($to, $subject, $finalMessage, implode("\r\n", $headers));

    if ($result) {
        logMessage("Email envoyé avec succès (PHP mail)", [
            'to' => $to,
            'subject' => $subject,
            'from' => $from
        ]);
        return true;
    } else {
        logMessage("Échec de l'envoi d'email (PHP mail)", [
            'to' => $to,
            'subject' => $subject,
            'from' => $from
        ], 'error');
        return false;
    }
}

/**
 * Simule l'envoi d'email en loggant seulement (mode développement)
 */
function sendMailViaLog($to, $subject, $message, $options, $from, $fromName) {
    logMessage("Email simulé (mode log)", [
        'to' => $to,
        'from' => $from,
        'fromName' => $fromName,
        'subject' => $subject,
        'message' => substr($message, 0, 200) . (strlen($message) > 200 ? '...' : ''),
        'options' => $options
    ], 'info');

    // En mode développement, toujours retourner true pour simuler un envoi réussi
    return true;
}

/**
 * Envoie un email via SMTP
 */
function sendMailViaSMTP($to, $subject, $message, $options, $emailConfig) {
    $smtp = $emailConfig['smtp'];

    // Pour l'instant, on utilise la méthode PHP mail() même pour SMTP
    // Dans une implémentation complète, on utiliserait une bibliothèque comme PHPMailer
    logMessage("SMTP configuré mais utilisation de PHP mail() pour l'instant", [
        'to' => $to,
        'smtp_host' => $smtp['host']
    ], 'info');

    // Fallback vers PHP mail()
    $from = isset($emailConfig['from']['email']) ? $emailConfig['from']['email'] : '<EMAIL>';
    $fromName = isset($emailConfig['from']['name']) ? $emailConfig['from']['name'] : 'Service WiFi Moulin';

    return sendMailViaPhpMail($to, $subject, $message, $options, $from, $fromName);
}
