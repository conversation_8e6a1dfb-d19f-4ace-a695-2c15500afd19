# Script PowerShell pour basculer l'application React sur le port 8080
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Configuration React App sur port 8080" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# 1. Arrêter les processus Node.js existants
Write-Host "`n1. Arrêt des processus Node.js existants..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2

# 2. Arrêter Apache (XAMPP)
Write-Host "2. Arrêt d'Apache (XAMPP)..." -ForegroundColor Yellow
Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2

# 3. Vérifier que le port 8080 est libre
Write-Host "3. Vérification du port 8080..." -ForegroundColor Yellow
$port8080 = Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue
if ($port8080) {
    Write-Host "Port 8080 encore occupé, arrêt forcé..." -ForegroundColor Red
    foreach ($conn in $port8080) {
        Stop-Process -Id $conn.OwningProcess -Force -ErrorAction SilentlyContinue
    }
    Start-Sleep -Seconds 2
}

# 4. Modifier la configuration Vite pour forcer le port 8080
Write-Host "4. Configuration Vite pour port 8080..." -ForegroundColor Yellow
$viteConfig = @"
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    strictPort: true,
    proxy: {
      // Redirige tous les appels commençant par /api vers le backend PHP
      '/api': {
        target: 'http://localhost/Gestion_moulin_wifiZone_ok/api',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
"@

$viteConfig | Out-File -FilePath "vite.config.ts" -Encoding UTF8

# 5. Modifier la configuration API
Write-Host "5. Configuration API pour port 8080..." -ForegroundColor Yellow
$configJs = Get-Content "src/config.js" -Raw
$configJs = $configJs -replace "window\.location\.port === '3000' \|\| window\.location\.port === '3001'", "window.location.port === '8080'"
$configJs | Out-File -FilePath "src/config.js" -Encoding UTF8

Write-Host "`n6. Démarrage de l'application React..." -ForegroundColor Green
Write-Host "L'application sera disponible sur: http://localhost:8080" -ForegroundColor Green
Write-Host "Appuyez sur Ctrl+C pour arrêter l'application" -ForegroundColor Yellow

# Démarrer l'application
npm run dev
