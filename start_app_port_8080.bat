@echo off
echo ========================================
echo Configuration pour port 8080
echo ========================================

echo 1. Arret d'Apache (XAMPP)...
taskkill /F /IM httpd.exe 2>nul
timeout /t 2 >nul

echo 2. Verification que le port 8080 est libre...
netstat -ano | findstr :8080
if %errorlevel% == 0 (
    echo Port 8080 encore occupe, tentative d'arret force...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080') do taskkill /F /PID %%a 2>nul
    timeout /t 2 >nul
)

echo 3. Demarrage de l'application React sur port 8080...
npm run dev

pause
