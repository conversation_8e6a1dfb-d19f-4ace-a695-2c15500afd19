<?php
/**
 * Gestionnaire CORS pour les requêtes API
 * Permet les requêtes cross-origin depuis le frontend (React)
 */

function configureCors() {
    // Autoriser l'accès depuis n'importe quelle origine
    // En production, remplacer * par le domaine spécifique de l'application frontend
    // En-tête CORS géré par le fichier cors.php principal
    require_once __DIR__ . '/../cors.php';
    
    // Méthodes HTTP autorisées
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    
    // En-têtes autorisés (complet pour éviter les problèmes CORS)
    header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control');
    
    // Permet l'envoi de cookies dans les requêtes cross-origin (si nécessaire)
    header('Access-Control-Allow-Credentials: true');
    
    // Cache la durée de validité des pré-vérifications CORS (en secondes)
    header('Access-Control-Max-Age: 86400'); // 24 heures
    
    // Type de contenu par défaut
    header('Content-Type: application/json; charset=UTF-8');
    
    // Si c'est une requête OPTIONS (pré-vérification CORS), terminer immédiatement
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit(0);
    }
}
