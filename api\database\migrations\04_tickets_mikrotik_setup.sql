-- Mo<PERSON>le <PERSON>ente de Tickets Mikrotik
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des clients
CREATE TABLE IF NOT EXISTS `clients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(100) NOT NULL,
  `prenom` varchar(100) DEFAULT NULL,
  `telephone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `adresse` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `telephone` (`telephone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des routeurs/hotspots Mikrotik
CREATE TABLE IF NOT EXISTS `mikrotik_hotspots` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `moulin_id` int(11) NOT NULL,
  `nom` varchar(100) NOT NULL,
  `adresse_ip` varchar(45) NOT NULL,
  `port` int(11) NOT NULL DEFAULT 8728,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `ssl` tinyint(1) NOT NULL DEFAULT 0,
  `actif` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des tickets Internet (révisé avec type)
CREATE TABLE IF NOT EXISTS `tickets_internet` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(20) NOT NULL,
  `type` varchar(50) NOT NULL DEFAULT 'Standard',
  `duree_validite` int(11) NOT NULL COMMENT 'Durée en heures',
  `prix_vente` decimal(10,2) NOT NULL,
  `date_creation` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_activation` datetime DEFAULT NULL,
  `date_expiration` datetime DEFAULT NULL,
  `hotspot_id` int(11) NOT NULL,
  `client_id` int(11) DEFAULT NULL,
  `vendu_par` int(11) NOT NULL,
  `statut` enum('disponible','vendu','active','expire') NOT NULL DEFAULT 'disponible',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  FOREIGN KEY (`hotspot_id`) REFERENCES `mikrotik_hotspots` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`vendu_par`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des ventes de tickets
CREATE TABLE IF NOT EXISTS `ventes_tickets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `moulin_id` int(11) NOT NULL,
  `prix_vente` decimal(10,2) NOT NULL,
  `commission_vendeur` decimal(10,2) DEFAULT NULL,
  `methode_paiement` enum('especes','mobile_money','carte') NOT NULL DEFAULT 'especes',
  `reference_paiement` varchar(50) DEFAULT NULL,
  `date_vente` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`ticket_id`) REFERENCES `tickets_internet` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des notifications clients
CREATE TABLE IF NOT EXISTS `notifications_clients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `ticket_id` int(11) NOT NULL,
  `type` enum('bienvenue','rappel_expiration','expiration') NOT NULL,
  `message` text NOT NULL,
  `canal` enum('sms','email','systeme') NOT NULL DEFAULT 'systeme',
  `statut` enum('en_attente','envoye','echec') NOT NULL DEFAULT 'en_attente',
  `date_creation` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_envoi` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`ticket_id`) REFERENCES `tickets_internet` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des forfaits
CREATE TABLE IF NOT EXISTS `forfaits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `duree` int(11) NOT NULL COMMENT 'Durée en heures',
  `prix` decimal(10,2) NOT NULL,
  `limite_vitesse` varchar(20) DEFAULT NULL COMMENT 'Format: download/upload (ex: 2M/1M)',
  `limite_donnees` int(11) DEFAULT NULL COMMENT 'Limite en Mo',
  `actif` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nom` (`nom`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertion d'un hotspot Mikrotik de test
INSERT IGNORE INTO `mikrotik_hotspots` (`moulin_id`, `nom`, `adresse_ip`, `port`, `username`, `password`, `ssl`, `actif`)
SELECT m.id, CONCAT(m.nom, ' Hotspot'), '************', 8728, 'admin', 'password', 0, 1
FROM `moulins` m WHERE m.code = 'M001';

-- Insertion de quelques forfaits par défaut
INSERT IGNORE INTO `forfaits` (`nom`, `description`, `duree`, `prix`, `limite_vitesse`, `limite_donnees`, `actif`) VALUES
('Journalier', 'Accès Internet pour 24h', 24, 500.00, '2M/1M', 1024, 1),
('Hebdomadaire', 'Accès Internet pour 7 jours', 168, 2500.00, '3M/1M', 5120, 1),
('Mensuel', 'Accès Internet pour 30 jours', 720, 8000.00, '5M/2M', 20480, 1);
