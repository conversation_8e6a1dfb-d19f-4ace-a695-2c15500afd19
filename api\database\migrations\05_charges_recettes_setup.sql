-- Module Charges & Recettes
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des catégories de charges
CREATE TABLE IF NOT EXISTS `categorie_charges` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nom` (`nom`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des charges
CREATE TABLE IF NOT EXISTS `charges` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `moulin_id` int(11) NOT NULL,
  `categorie_id` int(11) NOT NULL,
  `description` varchar(255) NOT NULL,
  `montant` decimal(10,2) NOT NULL,
  `date_charge` date NOT NULL,
  `periodicite` enum('ponctuelle','mensuelle','trimestrielle','annuelle') NOT NULL DEFAULT 'ponctuelle',
  `reference_facture` varchar(100) DEFAULT NULL,
  `pieces_justificatives` varchar(255) DEFAULT NULL COMMENT 'Chemin vers les fichiers justificatifs',
  `enregistre_par` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`categorie_id`) REFERENCES `categorie_charges` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`enregistre_par`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des catégories de recettes
CREATE TABLE IF NOT EXISTS `categorie_recettes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nom` (`nom`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des recettes
CREATE TABLE IF NOT EXISTS `recettes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `moulin_id` int(11) NOT NULL,
  `categorie_id` int(11) NOT NULL,
  `description` varchar(255) NOT NULL,
  `montant` decimal(10,2) NOT NULL,
  `date_recette` date NOT NULL,
  `reference` varchar(100) DEFAULT NULL,
  `enregistre_par` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`categorie_id`) REFERENCES `categorie_recettes` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`enregistre_par`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des bilans financiers
CREATE TABLE IF NOT EXISTS `bilans_financiers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `moulin_id` int(11) NOT NULL,
  `periode` varchar(7) NOT NULL COMMENT 'Format: YYYY-MM',
  `total_recettes` decimal(12,2) NOT NULL DEFAULT 0,
  `total_charges` decimal(12,2) NOT NULL DEFAULT 0,
  `benefice_net` decimal(12,2) NOT NULL DEFAULT 0,
  `taux_rentabilite` decimal(5,2) DEFAULT NULL COMMENT 'En pourcentage',
  `genere_par` int(11) NOT NULL,
  `statut` enum('provisoire','finalise') NOT NULL DEFAULT 'provisoire',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `moulin_periode` (`moulin_id`, `periode`),
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`genere_par`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertion des catégories de charges par défaut
INSERT IGNORE INTO `categorie_charges` (`nom`, `description`) VALUES
('Loyer', 'Charges liées au loyer du local'),
('Électricité', 'Factures d''électricité'),
('Salaires', 'Salaires du personnel'),
('Maintenance', 'Entretien et réparation des équipements'),
('Fournitures', 'Achats de fournitures diverses'),
('Taxes', 'Taxes et impôts'),
('Internet', 'Frais de connexion Internet'),
('Divers', 'Autres dépenses non catégorisées');

-- Insertion des catégories de recettes par défaut
INSERT IGNORE INTO `categorie_recettes` (`nom`, `description`) VALUES
('Vente Tickets WiFi', 'Recettes issues de la vente de tickets WiFi'),
('Services Mouture', 'Recettes des services de mouture'),
('Transferts', 'Commissions sur opérations de transfert'),
('Autres Services', 'Recettes provenant d''autres services');
