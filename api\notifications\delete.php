<?php
/**
 * delete.php
 * 
 * API endpoint pour supprimer une not
// Utiliser le système CORS centralisé
require_once __DIR__ . '/../cors.php';

ification de ticket
 */

// Configuration pour assurer que les erreurs PHP sont retournées en JSON
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Inclusion de la fonction de log
require_once '../utils/logger.php';

// En-têtes CORS et JSON pour toutes les requêtes
// Retiré: // Retiré: // Commenté pour éviter duplication: // En-tête CORS géré par cors.php
// Retiré: // Retiré: // Commenté pour éviter duplication: header("Access-Control-Allow-Methods: POST, OPTIONS");
// Retiré: // Retiré: // Commenté pour éviter duplication: header("Access-Control-Max-Age: 3600");
// Retiré: // Retiré: // Commenté pour éviter duplication: header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Ajouter Content-Type uniquement pour les réponses non-OPTIONS
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    // Commenté pour éviter duplication: // Commenté pour éviter duplication: header("Content-Type: application/json; charset=UTF-8");
}

// Gestion spéciale pour les requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Vérifier si c'est une requête POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Méthode non autorisée. Utilisez POST.'
    ]);
    exit;
}

// Récupération des données
$jsonData = file_get_contents("php://input");
$data = json_decode($jsonData);

// Validation des données
if (!$data || !isset($data->notification_id)) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error', 
        'message' => 'Données incomplètes: notification_id requis'
    ]);
    logMessage("Données incomplètes pour supprimer la notification", [
        'reçu' => json_decode($jsonData, true),
        'attendu' => 'notification_id'
    ], 'error');
    exit;
}

try {
    // Connexion à la base de données
    require_once '../config.php';
    
    // Vérifier que la notification existe
    $stmt = $pdo->prepare("SELECT * FROM notifications WHERE id = ?");
    $stmt->execute([$data->notification_id]);
    $notification = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$notification) {
        throw new Exception("Notification non trouvée");
    }
    
    // Supprimer la notification
    $stmt = $pdo->prepare("DELETE FROM notifications WHERE id = ?");
    $stmt->execute([$data->notification_id]);
    
    if ($stmt->rowCount() > 0) {
        // Réponse de succès
        echo json_encode([
            'status' => 'success',
            'message' => 'Notification supprimée avec succès'
        ]);
        
        logMessage("Notification supprimée", [
            'notification_id' => $data->notification_id,
            'type' => $notification['type']
        ]);
    } else {
        // Si aucune ligne n'a été supprimée
        throw new Exception("Impossible de supprimer la notification");
    }
    
} catch (Exception $e) {
    // Journaliser l'erreur
    logMessage("Erreur lors de la suppression de la notification", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'notification_id' => isset($data->notification_id) ? $data->notification_id : 'unknown'
    ], 'error');
    
    // Réponse d'erreur
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Erreur lors de la suppression: ' . $e->getMessage()
    ]);
}
?>
