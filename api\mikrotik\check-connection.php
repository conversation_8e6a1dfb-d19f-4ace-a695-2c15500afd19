<?php
/**
 * check-connection.php
 * 
 * API endpoint pour vérifier la connexion à un routeur Mikrotik
 */

// Configuration pour assurer que les erreurs PHP sont retournées en JSON
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Inclusion de la fonction de log
require_once '../utils/logger.php';

// En-têtes CORS et JSON pour toutes les requêtes
// En-tête CORS géré par le fichier cors.php principal
require_once __DIR__ . '/../cors.php';
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Ajouter Content-Type uniquement pour les réponses non-OPTIONS
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    header("Content-Type: application/json; charset=UTF-8");
}

// Gestion spéciale pour les requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Vérifier si c'est une requête POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Méthode non autorisée. Utilisez POST.'
    ]);
    exit;
}

// Récupération des données
$jsonData = file_get_contents("php://input");
$data = json_decode($jsonData);

// Validation des données
if (!$data || !isset($data->router_id)) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error', 
        'message' => 'Données incomplètes: router_id requis'
    ]);
    logMessage("Données incomplètes pour la vérification de connexion", [
        'reçu' => json_decode($jsonData, true),
        'attendu' => 'router_id'
    ], 'error');
    exit;
}

try {
    // Connexion à la base de données
    require_once '../config.php';
    
    // Récupérer les informations du routeur
    $stmt = $pdo->prepare("SELECT * FROM mikrotik_routers WHERE id = ?");
    $stmt->execute([$data->router_id]);
    $router = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$router) {
        throw new Exception("Routeur non trouvé");
    }
    
    // Connexion à l'API Mikrotik via la bibliothèque RouterOS API
    require_once '../lib/routeros_api.class.php';
    $mikrotik = new RouterOS\API([
        'host' => $router['address'],
        'user' => $router['username'],
        'pass' => $router['password'],
        'port' => $router['port'],
        'timeout' => 5 // Timeout court pour la vérification
    ]);
    
    // Tester la connexion
    $connected = $mikrotik->connect();
    
    if ($connected) {
        // Récupérer des informations de base du routeur pour confirmer
        $routerInfo = $mikrotik->sendSync(new RouterOS\Request('/system/resource/print'));
        
        // Mettre à jour le statut du routeur
        $stmt = $pdo->prepare("UPDATE mikrotik_routers SET status = 'online', last_check = NOW() WHERE id = ?");
        $stmt->execute([$router['id']]);
        
        // Déconnexion propre
        $mikrotik->disconnect();
        
        // Réponse de succès avec informations du routeur
        echo json_encode([
            'success' => true,
            'status' => 'online',
            'message' => 'Connexion réussie au routeur Mikrotik',
            'router_info' => [
                'name' => $router['name'],
                'system_name' => isset($routerInfo[0]['board-name']) ? $routerInfo[0]['board-name'] : 'N/A',
                'version' => isset($routerInfo[0]['version']) ? $routerInfo[0]['version'] : 'N/A',
                'uptime' => isset($routerInfo[0]['uptime']) ? $routerInfo[0]['uptime'] : 'N/A'
            ]
        ]);
        
        logMessage("Connexion réussie au routeur Mikrotik", [
            'router_id' => $router['id'],
            'name' => $router['name']
        ]);
    } else {
        // Mettre à jour le statut du routeur
        $stmt = $pdo->prepare("UPDATE mikrotik_routers SET status = 'offline', last_check = NOW() WHERE id = ?");
        $stmt->execute([$router['id']]);
        
        // Réponse d'échec
        echo json_encode([
            'success' => false,
            'status' => 'offline',
            'message' => 'Impossible de se connecter au routeur Mikrotik'
        ]);
        
        logMessage("Échec de connexion au routeur Mikrotik", [
            'router_id' => $router['id'],
            'name' => $router['name']
        ], 'warning');
    }
    
} catch (Exception $e) {
    // Mettre à jour le statut du routeur en cas d'erreur
    if (isset($router) && isset($router['id'])) {
        try {
            $stmt = $pdo->prepare("UPDATE mikrotik_routers SET status = 'error', last_check = NOW() WHERE id = ?");
            $stmt->execute([$router['id']]);
        } catch (Exception $updateError) {
            logMessage("Impossible de mettre à jour le statut du routeur", [
                'error' => $updateError->getMessage()
            ], 'error');
        }
    }
    
    // Journaliser l'erreur
    logMessage("Erreur lors de la vérification de connexion Mikrotik", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], 'error');
    
    // Réponse d'erreur
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'status' => 'error',
        'message' => 'Erreur de connexion: ' . $e->getMessage()
    ]);
}
?>
