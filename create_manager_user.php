<?php
header('Content-Type: text/html; charset=utf-8');
require_once 'api/config.php';

echo "<h1>🔧 Création utilisateur Manager</h1>";

try {
    // 1. Vérifier si l'utilisateur manager existe
    $stmt = $pdo->prepare("SELECT id, username FROM users WHERE username = 'manager'");
    $stmt->execute();
    $manager = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($manager) {
        echo "✅ Utilisateur manager existe déjà (ID: {$manager['id']})<br>";
    } else {
        echo "Création de l'utilisateur manager...<br>";
        
        // Vérifier si le rôle manager existe
        $stmt = $pdo->prepare("SELECT id FROM roles WHERE name = 'manager'");
        $stmt->execute();
        $role = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$role) {
            // C<PERSON><PERSON> le rôle manager
            $stmt = $pdo->prepare("INSERT INTO roles (name, description) VALUES ('manager', 'Manager avec accès aux moulins')");
            $stmt->execute();
            $role_id = $pdo->lastInsertId();
            echo "✅ Rôle manager créé (ID: $role_id)<br>";
        } else {
            $role_id = $role['id'];
            echo "✅ Rôle manager existe (ID: $role_id)<br>";
        }
        
        // Créer l'utilisateur manager
        $password_hash = password_hash('manager', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, nom, email, telephone, password, role_id, statut) VALUES (?, ?, ?, ?, ?, ?, 'Actif')");
        $stmt->execute(['manager', 'Manager Test', '<EMAIL>', '90000000', $password_hash, $role_id]);
        $manager_id = $pdo->lastInsertId();
        
        echo "✅ Utilisateur manager créé (ID: $manager_id)<br>";
        echo "   - Username: manager<br>";
        echo "   - Password: manager<br>";
        echo "   - Email: <EMAIL><br>";
    }
    
    // 2. Vérifier les gérants assignés
    $manager_id = $manager ? $manager['id'] : $manager_id;
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM gerants WHERE manager_id = ?");
    $stmt->execute([$manager_id]);
    $gerant_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "<br><h2>Gérants assignés au manager</h2>";
    echo "Nombre de gérants: $gerant_count<br>";
    
    if ($gerant_count == 0) {
        echo "<p><a href='/setup_manager_data.php'>Configurer les gérants et moulins</a></p>";
    }
    
    // 3. Vérifier les moulins visibles
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM moulins m 
        INNER JOIN gerants g ON m.gerant_id = g.id 
        WHERE g.manager_id = ?
    ");
    $stmt->execute([$manager_id]);
    $moulin_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "<br><h2>Moulins visibles par le manager</h2>";
    echo "Nombre de moulins: $moulin_count<br>";
    
    if ($moulin_count == 0) {
        echo "<p><a href='/setup_manager_data.php'>Configurer les moulins</a></p>";
    }
    
    echo "<br><h2>Actions</h2>";
    echo "<p><a href='http://localhost:8081' target='_blank'>Se connecter à l'application</a></p>";
    echo "<p><a href='http://localhost:8081/manager/dashboard/moulins' target='_blank'>Accéder aux moulins (après connexion)</a></p>";
    echo "<p><a href='/api/moulins.php?manager_id=$manager_id' target='_blank'>Tester l'API moulins</a></p>";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "<br>";
    echo "Fichier: " . $e->getFile() . "<br>";
    echo "Ligne: " . $e->getLine() . "<br>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
a { color: #007cba; text-decoration: none; padding: 5px 10px; background: #f0f0f0; border-radius: 3px; }
a:hover { background: #e0e0e0; }
</style>
