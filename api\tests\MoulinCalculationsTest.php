<?php

use PHPUnit\Framework\TestCase;

/**
 * Tests unitaires pour les fonctions de calcul liées au modèle Moulin
 * Ces tests couvrent les fonctions de calcul de production et de rentabilité
 */
class MoulinCalculationsTest extends TestCase
{
    private $pdo;
    private $moulinId;
    
    protected function setUp(): void
    {
        // Initialiser la connexion à la base de données de test
        $this->pdo = getTestDbConnection();
        
        // Créer un moulin de test pour les calculs
        $stmt = $this->pdo->prepare("
            INSERT INTO moulins (nom, emplacement, type, puissance, statut, dateAchat, coutAchat)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            'Moulin Test Calcul',
            'Localisation Test',
            'Électrique',
            '5kW',
            'Actif',
            date('Y-m-d', strtotime('-1 year')),
            500000
        ]);
        
        $this->moulinId = $this->pdo->lastInsertId();
        
        // Ajouter des recettes pour le moulin
        $this->setupRecettes();
        
        // Ajouter des charges pour le moulin
        $this->setupCharges();
        
        // Ajouter des données de production pour le moulin
        $this->setupProduction();
    }
    
    /**
     * Configuration des recettes de test
     */
    private function setupRecettes(): void
    {
        // Créer des recettes sur différentes périodes
        $recettesData = [
            // Recettes du mois précédent
            ['date' => date('Y-m-d', strtotime('-1 month')), 'montant' => 35000],
            ['date' => date('Y-m-d', strtotime('-25 days')), 'montant' => 42000],
            ['date' => date('Y-m-d', strtotime('-20 days')), 'montant' => 38000],
            
            // Recettes du mois courant
            ['date' => date('Y-m-d', strtotime('-15 days')), 'montant' => 40000],
            ['date' => date('Y-m-d', strtotime('-10 days')), 'montant' => 45000],
            ['date' => date('Y-m-d', strtotime('-5 days')), 'montant' => 50000],
            ['date' => date('Y-m-d'), 'montant' => 30000],
        ];
        
        $stmt = $this->pdo->prepare("
            INSERT INTO recettes (moulin_id, date, montant, source)
            VALUES (?, ?, ?, ?)
        ");
        
        foreach ($recettesData as $recette) {
            $stmt->execute([
                $this->moulinId,
                $recette['date'],
                $recette['montant'],
                'Mouture'
            ]);
        }
    }
    
    /**
     * Configuration des charges de test
     */
    private function setupCharges(): void
    {
        // Créer des charges sur différentes périodes
        $chargesData = [
            // Charges du mois précédent
            ['date' => date('Y-m-d', strtotime('-1 month')), 'montant' => 15000, 'type' => 'Maintenance'],
            ['date' => date('Y-m-d', strtotime('-25 days')), 'montant' => 5000, 'type' => 'Électricité'],
            
            // Charges du mois courant
            ['date' => date('Y-m-d', strtotime('-15 days')), 'montant' => 8000, 'type' => 'Électricité'],
            ['date' => date('Y-m-d', strtotime('-5 days')), 'montant' => 12000, 'type' => 'Maintenance'],
            ['date' => date('Y-m-d'), 'montant' => 7000, 'type' => 'Salaire'],
        ];
        
        $stmt = $this->pdo->prepare("
            INSERT INTO charges (moulin_id, date, montant, type)
            VALUES (?, ?, ?, ?)
        ");
        
        foreach ($chargesData as $charge) {
            $stmt->execute([
                $this->moulinId,
                $charge['date'],
                $charge['montant'],
                $charge['type']
            ]);
        }
    }
    
    /**
     * Configuration des données de production de test
     */
    private function setupProduction(): void
    {
        // Créer des données de production sur différentes périodes
        $productionData = [
            // Production du mois précédent
            ['date' => date('Y-m-d', strtotime('-1 month')), 'quantite' => 500],
            ['date' => date('Y-m-d', strtotime('-25 days')), 'quantite' => 600],
            ['date' => date('Y-m-d', strtotime('-20 days')), 'quantite' => 550],
            
            // Production du mois courant
            ['date' => date('Y-m-d', strtotime('-15 days')), 'quantite' => 580],
            ['date' => date('Y-m-d', strtotime('-10 days')), 'quantite' => 620],
            ['date' => date('Y-m-d', strtotime('-5 days')), 'quantite' => 700],
            ['date' => date('Y-m-d'), 'quantite' => 450],
        ];
        
        $stmt = $this->pdo->prepare("
            INSERT INTO productions (moulin_id, date, quantite, unite)
            VALUES (?, ?, ?, ?)
        ");
        
        foreach ($productionData as $production) {
            $stmt->execute([
                $this->moulinId,
                $production['date'],
                $production['quantite'],
                'kg'
            ]);
        }
    }

    /**
     * Test de calcul des recettes totales sur une période donnée
     */
    public function testCalculRecettesTotales()
    {
        // Appel à la fonction à tester
        require_once dirname(__DIR__) . '/fonctions/moulin_calculs.php';
        
        // Test sur le mois courant
        $debutMoisCourant = date('Y-m-01');
        $finMoisCourant = date('Y-m-t');
        $recettesTotalesMoisCourant = calculRecettesTotales($this->moulinId, $debutMoisCourant, $finMoisCourant);
        
        // La somme attendue est 40000 + 45000 + 50000 + 30000 = 165000
        $this->assertEquals(165000, $recettesTotalesMoisCourant);
        
        // Test sur le mois précédent
        $debutMoisPrecedent = date('Y-m-01', strtotime('-1 month'));
        $finMoisPrecedent = date('Y-m-t', strtotime('-1 month'));
        $recettesTotalesMoisPrecedent = calculRecettesTotales($this->moulinId, $debutMoisPrecedent, $finMoisPrecedent);
        
        // La somme attendue est 35000 + 42000 + 38000 = 115000
        $this->assertEquals(115000, $recettesTotalesMoisPrecedent);
    }
    
    /**
     * Test de calcul des charges totales sur une période donnée
     */
    public function testCalculChargesTotales()
    {
        // Appel à la fonction à tester
        require_once dirname(__DIR__) . '/fonctions/moulin_calculs.php';
        
        // Test sur le mois courant
        $debutMoisCourant = date('Y-m-01');
        $finMoisCourant = date('Y-m-t');
        $chargesTotalesMoisCourant = calculChargesTotales($this->moulinId, $debutMoisCourant, $finMoisCourant);
        
        // La somme attendue est 8000 + 12000 + 7000 = 27000
        $this->assertEquals(27000, $chargesTotalesMoisCourant);
        
        // Test sur le mois précédent
        $debutMoisPrecedent = date('Y-m-01', strtotime('-1 month'));
        $finMoisPrecedent = date('Y-m-t', strtotime('-1 month'));
        $chargesTotalesMoisPrecedent = calculChargesTotales($this->moulinId, $debutMoisPrecedent, $finMoisPrecedent);
        
        // La somme attendue est 15000 + 5000 = 20000
        $this->assertEquals(20000, $chargesTotalesMoisPrecedent);
    }
    
    /**
     * Test de calcul de la rentabilité (bénéfice = recettes - charges) sur une période donnée
     */
    public function testCalculRentabilite()
    {
        // Appel à la fonction à tester
        require_once dirname(__DIR__) . '/fonctions/moulin_calculs.php';
        
        // Test sur le mois courant
        $debutMoisCourant = date('Y-m-01');
        $finMoisCourant = date('Y-m-t');
        $rentabiliteMoisCourant = calculRentabilite($this->moulinId, $debutMoisCourant, $finMoisCourant);
        
        // Le bénéfice attendu est 165000 - 27000 = 138000
        $this->assertEquals(138000, $rentabiliteMoisCourant['benefice']);
        // La marge attendue est (138000 / 165000) * 100 = 83.64%
        $this->assertEquals(83.64, $rentabiliteMoisCourant['marge'], 0.01);
        
        // Test sur le mois précédent
        $debutMoisPrecedent = date('Y-m-01', strtotime('-1 month'));
        $finMoisPrecedent = date('Y-m-t', strtotime('-1 month'));
        $rentabiliteMoisPrecedent = calculRentabilite($this->moulinId, $debutMoisPrecedent, $finMoisPrecedent);
        
        // Le bénéfice attendu est 115000 - 20000 = 95000
        $this->assertEquals(95000, $rentabiliteMoisPrecedent['benefice']);
        // La marge attendue est (95000 / 115000) * 100 = 82.61%
        $this->assertEquals(82.61, $rentabiliteMoisPrecedent['marge'], 0.01);
    }
    
    /**
     * Test de calcul de la production totale sur une période donnée
     */
    public function testCalculProductionTotale()
    {
        // Appel à la fonction à tester
        require_once dirname(__DIR__) . '/fonctions/moulin_calculs.php';
        
        // Test sur le mois courant
        $debutMoisCourant = date('Y-m-01');
        $finMoisCourant = date('Y-m-t');
        $productionTotaleMoisCourant = calculProductionTotale($this->moulinId, $debutMoisCourant, $finMoisCourant);
        
        // La somme attendue est 580 + 620 + 700 + 450 = 2350
        $this->assertEquals(2350, $productionTotaleMoisCourant);
        
        // Test sur le mois précédent
        $debutMoisPrecedent = date('Y-m-01', strtotime('-1 month'));
        $finMoisPrecedent = date('Y-m-t', strtotime('-1 month'));
        $productionTotaleMoisPrecedent = calculProductionTotale($this->moulinId, $debutMoisPrecedent, $finMoisPrecedent);
        
        // La somme attendue est 500 + 600 + 550 = 1650
        $this->assertEquals(1650, $productionTotaleMoisPrecedent);
    }
    
    /**
     * Test de calcul de la moyenne journalière de production sur une période donnée
     */
    public function testCalculMoyenneJournaliereProduction()
    {
        // Appel à la fonction à tester
        require_once dirname(__DIR__) . '/fonctions/moulin_calculs.php';
        
        // Test sur une période spécifique de 7 jours qui contient exactement 3 jours avec production
        $debut = date('Y-m-d', strtotime('-15 days'));
        $fin = date('Y-m-d', strtotime('-9 days'));
        $moyenneJournaliere = calculMoyenneJournaliereProduction($this->moulinId, $debut, $fin);
        
        // La moyenne attendue est (580 + 620) / 2 = 600 (seulement pour les jours avec production)
        $this->assertEquals(600, $moyenneJournaliere);
    }
    
    /**
     * Test de calcul de la rentabilité par unité de production (bénéfice/kg)
     */
    public function testCalculRentabiliteParUnite()
    {
        // Appel à la fonction à tester
        require_once dirname(__DIR__) . '/fonctions/moulin_calculs.php';
        
        // Test sur le mois courant
        $debutMoisCourant = date('Y-m-01');
        $finMoisCourant = date('Y-m-t');
        $rentabiliteParUnite = calculRentabiliteParUnite($this->moulinId, $debutMoisCourant, $finMoisCourant);
        
        // Le bénéfice par kg attendu est 138000 / 2350 = 58.72 par kg
        $this->assertEquals(58.72, $rentabiliteParUnite, 0.01);
    }
    
    /**
     * Test de calcul du retour sur investissement (ROI)
     */
    public function testCalculROI()
    {
        // Appel à la fonction à tester
        require_once dirname(__DIR__) . '/fonctions/moulin_calculs.php';
        
        // Calculer le ROI depuis la date d'achat du moulin
        $dateAchat = date('Y-m-d', strtotime('-1 year'));
        $aujourdhui = date('Y-m-d');
        $roi = calculROI($this->moulinId, $dateAchat, $aujourdhui);
        
        // Vérifier les propriétés du ROI
        $this->assertArrayHasKey('pourcentage', $roi);
        $this->assertArrayHasKey('annualise', $roi);
        $this->assertArrayHasKey('tempsRemboursement', $roi);
        
        // Le ROI ne peut pas être précisément prédit car il dépend des données générées,
        // mais il devrait être positif dans notre cas de test
        $this->assertGreaterThan(0, $roi['pourcentage']);
    }
    
    protected function tearDown(): void
    {
        // Supprimer toutes les données de test
        $this->pdo->exec("DELETE FROM productions WHERE moulin_id = {$this->moulinId}");
        $this->pdo->exec("DELETE FROM charges WHERE moulin_id = {$this->moulinId}");
        $this->pdo->exec("DELETE FROM recettes WHERE moulin_id = {$this->moulinId}");
        $this->pdo->exec("DELETE FROM moulins WHERE id = {$this->moulinId}");
        
        $this->pdo = null;
    }
}
