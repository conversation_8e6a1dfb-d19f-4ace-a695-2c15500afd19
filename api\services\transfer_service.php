<?php

/**
 * Service de gestion des transferts d'argent
 * 
 * Gère les calculs de frais et les taux de change pour différents services de transfert.
 * Peut être étendu pour intégrer des APIs externes pour les taux de change.
 */
class TransferService {

    /**
     * Calcule les frais de transfert en fonction du service et du montant.
     *
     * @param string $service Le nom du service de transfert (ex: 'Mixx', 'Flooz').
     * @param float $amount Le montant du transfert.
     * @return float Les frais calculés.
     * @throws InvalidArgumentException Si les paramètres sont invalides
     */
    public function calculateFees(string $service, float $amount): float {
        // Validation des entrées
        if (empty($service)) {
            throw new InvalidArgumentException('Le service de transfert doit être spécifié');
        }
        
        if ($amount <= 0) {
            throw new InvalidArgumentException('Le montant doit être supérieur à zéro');
        }
        $fees = 0;
        $service = strtolower($service);

        // Grille tarifaire pour les services nationaux (exemple)
        $national_fees_grid = [
            ['min' => 1, 'max' => 5000, 'fee' => 100],
            ['min' => 5001, 'max' => 10000, 'fee' => 150],
            ['min' => 10001, 'max' => 25000, 'fee' => 250],
            ['min' => 25001, 'max' => 50000, 'fee' => 500],
            ['min' => 50001, 'max' => 100000, 'fee' => 800],
            ['min' => 100001, 'max' => 250000, 'fee' => 1200],
            ['min' => 250001, 'max' => 500000, 'fee' => 2000],
        ];

        // Grille pour les services internationaux (exemple)
        $international_fees_percentage = 0.05; // 5% de frais

        switch ($service) {
            case 'mixx by yass':
            case 'flooz':
            case 'moov':
            case 't-money':
                foreach ($national_fees_grid as $tier) {
                    if ($amount >= $tier['min'] && $amount <= $tier['max']) {
                        $fees = $tier['fee'];
                        break;
                    }
                }
                // Si le montant est supérieur au max de la grille, on peut appliquer un pourcentage
                if ($fees === 0 && $amount > 500000) {
                    $fees = $amount * 0.005; // 0.5% pour les gros montants
                }
                break;

            case 'western union':
            case 'wari':
            case 'ria':
            case 'moneygram':
                $fees = $amount * $international_fees_percentage;
                break;

            default:
                // Comportement par défaut si le service n'est pas reconnu
                // On pourrait retourner une erreur ou appliquer des frais par défaut
                $fees = 0; // Ou une valeur par défaut sécuritaire
                break;
        }

        return $fees;
    }

    /**
     * Récupère le taux de change pour une paire de devises.
     * Pour l'instant, simule un taux de change. À remplacer par une API externe.
     *
     * @param string $source_currency La devise source (ex: 'XOF').
     * @param string $destination_currency La devise de destination (ex: 'EUR').
     * @return float Le taux de change.
     * @throws InvalidArgumentException Si les devises sont invalides
     */
    public function getExchangeRate(string $source_currency, string $destination_currency): float {
        // Validation des entrées
        if (empty($source_currency) || empty($destination_currency)) {
            throw new InvalidArgumentException('Les devises source et destination doivent être spécifiées');
        }
        
        // Normalisation des codes de devise
        $source_currency = strtoupper(trim($source_currency));
        $destination_currency = strtoupper(trim($destination_currency));
        // Même devise = taux de 1.0
        if ($source_currency === $destination_currency) {
            return 1.0;
        }
        
        // Essayer d'abord de récupérer depuis le cache
        $cache_key = "exchange_rate_{$source_currency}_{$destination_currency}";
        $cached_rate = $this->getFromCache($cache_key);
        
        if ($cached_rate !== null) {
            return (float) $cached_rate;
        }
        
        // Possibilité d'intégrer une API externe ici
        // Pour l'instant, on utilise des taux simulés
        $rates = [
            'XOF_EUR' => 0.00152, // 1 EUR = 655.957 XOF
            'EUR_XOF' => 655.957,
            'XOF_USD' => 0.00165,
            'USD_XOF' => 605.0,
            'EUR_USD' => 1.08,
            'USD_EUR' => 0.92,
            'XOF_GHS' => 0.021,  // Cedi ghanéen
            'GHS_XOF' => 47.5
        ];

        $rate_key = "{$source_currency}_{$destination_currency}";
        
        // Si le taux direct n'existe pas, on essaie d'inverser la paire
        if (!isset($rates[$rate_key])) {
            $inverse_key = "{$destination_currency}_{$source_currency}";
            if (isset($rates[$inverse_key])) {
                $rate = 1 / $rates[$inverse_key];
                $this->saveToCache($cache_key, $rate, 3600); // Cache pendant 1 heure
                return $rate;
            }
            
            // On pourrait aussi essayer une conversion indirecte via une devise pivot (ex: USD)
            // Par exemple: XOF -> USD -> GHS si XOF -> GHS n'existe pas directement
            
            // Retourner une valeur par défaut sécurisée
            error_log("Taux de change non disponible pour {$rate_key}");
            return 1.0;
        }
        
        $rate = $rates[$rate_key];
        $this->saveToCache($cache_key, $rate, 3600); // Cache pendant 1 heure
        return $rate;
    }
    
    /**
     * Récupère une valeur du cache
     * 
     * @param string $key La clé de cache
     * @return mixed La valeur ou null si non trouvée
     */
    private function getFromCache(string $key) {
        // Implémentation simple basée sur un fichier
        $cache_dir = __DIR__ . '/../cache';
        $cache_file = $cache_dir . '/' . md5($key) . '.cache';
        
        // Vérifier si le fichier existe et n'est pas expiré
        if (file_exists($cache_file)) {
            $data = unserialize(file_get_contents($cache_file));
            if ($data['expires'] > time()) {
                return $data['value'];
            }
            // Supprimer le cache expiré
            @unlink($cache_file);
        }
        
        return null;
    }
    
    /**
     * Sauvegarde une valeur dans le cache
     * 
     * @param string $key La clé de cache
     * @param mixed $value La valeur à mettre en cache
     * @param int $ttl Durée de vie du cache en secondes
     * @return bool Succès ou échec
     */
    private function saveToCache(string $key, $value, int $ttl = 3600): bool {
        $cache_dir = __DIR__ . '/../cache';
        
        // Créer le répertoire de cache s'il n'existe pas
        if (!file_exists($cache_dir)) {
            if (!mkdir($cache_dir, 0755, true)) {
                return false;
            }
        }
        
        $cache_file = $cache_dir . '/' . md5($key) . '.cache';
        $data = [
            'value' => $value,
            'expires' => time() + $ttl
        ];
        
        return file_put_contents($cache_file, serialize($data)) !== false;
    }
    
    /**
     * Génère un code de transfert unique
     *
     * @param string $prefix Préfixe pour le code (ex: 'TR')
     * @return string Le code unique généré
     */
    public function generateTransferCode(string $prefix = 'TR'): string {
        $unique = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));
        return $prefix . $unique;
    }
}
