-- Module Rapports & Statistiques
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des rapports
CREATE TABLE IF NOT EXISTS `rapports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `titre` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `type` enum('ventes','financier','performances','activite','personnalise') NOT NULL,
  `parametres` json DEFAULT NULL COMMENT 'Configuration JSON du rapport',
  `periode_debut` date DEFAULT NULL,
  `periode_fin` date DEFAULT NULL,
  `createur_id` int(11) NOT NULL,
  `moulin_id` int(11) DEFAULT NULL COMMENT 'NULL si rapport global',
  `format` enum('html','csv','pdf','json') NOT NULL DEFAULT 'html',
  `fichier_genere` varchar(255) DEFAULT NULL COMMENT 'Chemin vers le fichier généré',
  `date_generation` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `cron_expression` varchar(100) DEFAULT NULL COMMENT 'Expression cron pour génération automatique',
  PRIMARY KEY (`id`),
  UNIQUE KEY `titre_unique` (`titre`),
  FOREIGN KEY (`createur_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des statistiques agrégées (pour mise en cache et amélioration des performances)
CREATE TABLE IF NOT EXISTS `statistiques_agregees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cle` varchar(100) NOT NULL,
  `type` varchar(50) NOT NULL,
  `periode` varchar(10) NOT NULL COMMENT 'Format: YYYY-MM ou YYYY-MM-DD',
  `moulin_id` int(11) DEFAULT NULL,
  `gerant_id` int(11) DEFAULT NULL,
  `valeur` decimal(12,2) NOT NULL,
  `metadonnees` json DEFAULT NULL COMMENT 'Données supplémentaires au format JSON',
  `date_calcul` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `stats_unique` (`cle`, `type`, `periode`, `moulin_id`, `gerant_id`),
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`gerant_id`) REFERENCES `gerants` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des vues de tableaux de bord sauvegardées
CREATE TABLE IF NOT EXISTS `dashboard_views` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `nom` varchar(100) NOT NULL,
  `configuration` json NOT NULL COMMENT 'Configuration JSON du tableau de bord',
  `par_defaut` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_view_name` (`user_id`, `nom`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Création des vues SQL pour faciliter la génération de rapports

-- Vue pour les ventes de tickets par jour et par moulin
CREATE OR REPLACE VIEW `vue_ventes_tickets_par_jour` AS
SELECT 
  v.moulin_id,
  m.nom AS nom_moulin,
  DATE(v.date_vente) AS date_vente,
  COUNT(v.id) AS nombre_tickets,
  SUM(v.prix_vente) AS montant_total,
  SUM(v.commission_vendeur) AS commission_totale
FROM ventes_tickets v
JOIN moulins m ON v.moulin_id = m.id
GROUP BY v.moulin_id, DATE(v.date_vente)
ORDER BY DATE(v.date_vente) DESC;

-- Vue pour les performances des gérants
CREATE OR REPLACE VIEW `vue_performances_gerants` AS
SELECT 
  p.gerant_id,
  g.nom AS nom_gerant,
  g.prenom AS prenom_gerant,
  p.moulin_id,
  m.nom AS nom_moulin,
  p.periode,
  p.ventes_total,
  p.objectif_ventes,
  p.taux_realisation,
  p.evaluation
FROM performances p
JOIN gerants g ON p.gerant_id = g.id
JOIN moulins m ON p.moulin_id = m.id
ORDER BY p.periode DESC, p.taux_realisation DESC;

-- Vue pour le bilan financier mensuel par moulin
CREATE OR REPLACE VIEW `vue_bilan_financier_mensuel` AS
SELECT 
  m.id AS moulin_id,
  m.nom AS nom_moulin,
  DATE_FORMAT(r.date_recette, '%Y-%m') AS periode,
  SUM(r.montant) AS total_recettes,
  (
    SELECT COALESCE(SUM(c.montant), 0)
    FROM charges c
    WHERE c.moulin_id = m.id AND DATE_FORMAT(c.date_charge, '%Y-%m') = DATE_FORMAT(r.date_recette, '%Y-%m')
  ) AS total_charges,
  SUM(r.montant) - (
    SELECT COALESCE(SUM(c.montant), 0)
    FROM charges c
    WHERE c.moulin_id = m.id AND DATE_FORMAT(c.date_charge, '%Y-%m') = DATE_FORMAT(r.date_recette, '%Y-%m')
  ) AS benefice_net,
  CASE 
    WHEN (
      SELECT COALESCE(SUM(c.montant), 0)
      FROM charges c
      WHERE c.moulin_id = m.id AND DATE_FORMAT(c.date_charge, '%Y-%m') = DATE_FORMAT(r.date_recette, '%Y-%m')
    ) > 0 THEN
      (SUM(r.montant) - (
        SELECT COALESCE(SUM(c.montant), 0)
        FROM charges c
        WHERE c.moulin_id = m.id AND DATE_FORMAT(c.date_charge, '%Y-%m') = DATE_FORMAT(r.date_recette, '%Y-%m')
      )) / (
        SELECT COALESCE(SUM(c.montant), 0)
        FROM charges c
        WHERE c.moulin_id = m.id AND DATE_FORMAT(c.date_charge, '%Y-%m') = DATE_FORMAT(r.date_recette, '%Y-%m')
      ) * 100
    ELSE 0
  END AS taux_rentabilite
FROM moulins m
JOIN recettes r ON m.id = r.moulin_id
GROUP BY m.id, DATE_FORMAT(r.date_recette, '%Y-%m')
ORDER BY DATE_FORMAT(r.date_recette, '%Y-%m') DESC;

-- Vue pour l'activité des hotspots par moulin
CREATE OR REPLACE VIEW `vue_activite_hotspots` AS
SELECT 
  m.id AS moulin_id,
  m.nom AS nom_moulin,
  h.id AS hotspot_id,
  h.nom AS nom_hotspot,
  DATE(t.date_activation) AS date,
  COUNT(t.id) AS nombre_activations,
  COUNT(DISTINCT t.client_id) AS nombre_clients_uniques
FROM moulins m
JOIN mikrotik_hotspots h ON m.id = h.moulin_id
JOIN tickets_internet t ON h.id = t.hotspot_id
WHERE t.date_activation IS NOT NULL
GROUP BY m.id, h.id, DATE(t.date_activation)
ORDER BY DATE(t.date_activation) DESC;

-- Création d'un rapport de test
INSERT IGNORE INTO `rapports` (`titre`, `description`, `type`, `parametres`, `periode_debut`, `periode_fin`, `createur_id`, `format`)
SELECT 
  'Rapport de ventes mensuel', 
  'Rapport détaillant les ventes de tickets par moulin pour le mois en cours', 
  'ventes',
  '{"groupBy": "moulin", "includeCharts": true}',
  DATE_FORMAT(NOW(), '%Y-%m-01'),
  LAST_DAY(NOW()),
  u.id,
  'html'
FROM users u 
WHERE u.username = 'admin'
LIMIT 1;
