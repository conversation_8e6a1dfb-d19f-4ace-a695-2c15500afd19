<?php
/**
 * synchronize.php
 * 
 * API endpoint pour synchroniser les tickets entre l'application et les routeurs Mikrotik
 * Cela permet de maintenir la cohérence entre la base de données et les routeurs
 */

// Configuration pour assurer que les erreurs PHP sont retournées en JSON
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Inclusion de la fonction de log
require_once '../utils/logger.php';

// En-têtes CORS et JSON pour toutes les requêtes
require_once __DIR__ . '/../cors.php';
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Ajouter Content-Type uniquement pour les réponses non-OPTIONS (sinon cela peut casser les requêtes preflight)
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    header("Content-Type: application/json; charset=UTF-8");
}

// Gestion spéciale pour les requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    // Répondre avec succès et les en-têtes CORS appropriés
    http_response_code(200);
    // Ne pas renvoyer de corps de réponse pour les requêtes OPTIONS
    exit;
}

// Vérifier si c'est une requête POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Méthode non autorisée. Utilisez POST.'
    ]);
    exit;
}

// Récupération des données
$jsonData = file_get_contents("php://input");
$data = json_decode($jsonData);

// Journaliser les données reçues
logMessage("Données de requête de synchronisation", json_decode($jsonData, true));

// Validation des données
if (!$data || !isset($data->router_id)) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error', 
        'message' => 'Données incomplètes: router_id requis'
    ]);
    logMessage("Données incomplètes pour la synchronisation", [
        'reçu' => json_decode($jsonData, true),
        'attendu' => 'router_id'
    ], 'error');
    exit;
}

try {
    // Connexion à la base de données
    require_once '../config.php';
    
    // Récupérer les informations du routeur
    $stmt = $pdo->prepare("SELECT * FROM mikrotik_routers WHERE id = ?");
    $stmt->execute([$data->router_id]);
    $router = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$router) {
        throw new Exception("Routeur non trouvé");
    }
    
    // Initialisation des compteurs pour le rapport
    $added = 0;
    $updated = 0;
    $deleted = 0;
    $errors = 0;
    
    // Connexion à l'API Mikrotik via la bibliothèque RouterOS API
    require_once '../lib/routeros_api.class.php';
    $mikrotik = new RouterOS\API([
        'host' => $router['address'],
        'user' => $router['username'],
        'pass' => $router['password'],
        'port' => $router['port'],
    ]);
    
    // Tester la connexion avant de continuer
    if (!$mikrotik->connect()) {
        throw new Exception("Impossible de se connecter au routeur Mikrotik");
    }
    
    logMessage("Connexion établie avec le routeur Mikrotik", [
        'router_id' => $router['id'],
        'name' => $router['name']
    ]);
    
    // 1. Récupération des tickets du routeur Mikrotik
    $mikrotikTickets = $mikrotik->sendSync(new RouterOS\Request('/ip/hotspot/user/print'));
    
    // Liste pour les tickets actifs sur Mikrotik
    $mikrotikTicketsIds = [];
    
    // Mettre à jour le statut du routeur
    $stmt = $pdo->prepare("UPDATE mikrotik_routers SET status = 'online', last_sync = NOW() WHERE id = ?");
    $stmt->execute([$router['id']]);
    
    // Commencer une transaction pour toutes les opérations de synchronisation
    $pdo->beginTransaction();
    
    // 2. Traiter chaque ticket du routeur Mikrotik
    foreach ($mikrotikTickets as $mikrotikTicket) {
        try {
            // Stocker l'ID pour la comparaison plus tard
            if (isset($mikrotikTicket['.id'])) {
                $mikrotikTicketsIds[] = $mikrotikTicket['.id'];
            }
            
            // Chercher si le ticket existe déjà dans la base de données
            $stmt = $pdo->prepare("SELECT * FROM tickets_internet WHERE mikrotik_id = ?");
            $stmt->execute([$mikrotikTicket['.id']]);
            $localTicket = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Le ticket n'existe pas localement, on l'ajoute
            if (!$localTicket) {
                // Déterminer le type de ticket en fonction du profil
                $stmt = $pdo->prepare("SELECT id FROM ticket_types WHERE mikrotik_profile = ? LIMIT 1");
                $stmt->execute([isset($mikrotikTicket['profile']) ? $mikrotikTicket['profile'] : 'default']);
                $ticketType = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$ticketType) {
                    // Si aucun type correspondant n'est trouvé, utiliser le type par défaut
                    $stmt = $pdo->prepare("SELECT id FROM ticket_types WHERE is_default = 1 LIMIT 1");
                    $stmt->execute();
                    $ticketType = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if (!$ticketType) {
                        // Si aucun type par défaut n'existe, ignorer ce ticket
                        continue;
                    }
                }
                
                // Déterminer le statut du ticket
                $status = 'active';
                if (isset($mikrotikTicket['disabled']) && $mikrotikTicket['disabled'] == 'true') {
                    $status = 'disabled';
                } else if (isset($mikrotikTicket['limit-uptime']) && isset($mikrotikTicket['uptime'])) {
                    // Vérifier si le ticket a expiré
                    // Note: La logique exacte dépend du format des champs 'uptime' et 'limit-uptime'
                    $status = 'active'; // À adapter selon vos besoins
                }
                
                // Insérer le nouveau ticket dans la base de données
                $stmt = $pdo->prepare("
                    INSERT INTO tickets_internet 
                    (mikrotik_id, code, type_ticket_id, libelle, prix, statut, 
                     nom_client, date_creation, date_activation, date_expiration)
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NULL)
                ");
                $stmt->execute([
                    $mikrotikTicket['.id'],
                    $mikrotikTicket['name'] ?? ('ticket_' . uniqid()),
                    $ticketType['id'],
                    'Ticket importé de Mikrotik',
                    0, // Prix inconnu pour les tickets importés
                    $status,
                    $mikrotikTicket['comment'] ?? 'Client inconnu'
                ]);
                
                $added++;
                logMessage("Ticket Mikrotik ajouté à la base de données", [
                    'mikrotik_id' => $mikrotikTicket['.id'],
                    'name' => $mikrotikTicket['name'] ?? 'unknown'
                ]);
            } else {
                // Le ticket existe déjà, mettre à jour son statut si nécessaire
                $newStatus = $localTicket['statut'];
                
                if (isset($mikrotikTicket['disabled']) && $mikrotikTicket['disabled'] == 'true') {
                    $newStatus = 'disabled';
                } else if (isset($mikrotikTicket['limit-uptime']) && isset($mikrotikTicket['uptime'])) {
                    // Logique pour vérifier l'expiration
                    $newStatus = 'active'; // À adapter
                }
                
                if ($newStatus !== $localTicket['statut']) {
                    $stmt = $pdo->prepare("UPDATE tickets_internet SET statut = ? WHERE id = ?");
                    $stmt->execute([$newStatus, $localTicket['id']]);
                    $updated++;
                    
                    logMessage("Statut de ticket mis à jour", [
                        'ticket_id' => $localTicket['id'],
                        'old_status' => $localTicket['statut'],
                        'new_status' => $newStatus
                    ]);
                }
            }
        } catch (Exception $ticketError) {
            $errors++;
            logMessage("Erreur lors du traitement d'un ticket Mikrotik", [
                'error' => $ticketError->getMessage(),
                'ticket' => isset($mikrotikTicket['.id']) ? $mikrotikTicket['.id'] : 'unknown'
            ], 'error');
            // Continuer avec le ticket suivant
        }
    }
    
    // 3. Vérifier les tickets qui existent dans la base mais plus sur le routeur
    $stmt = $pdo->prepare("
        SELECT id, mikrotik_id FROM tickets_internet 
        WHERE mikrotik_router_id = ? AND mikrotik_id IS NOT NULL
    ");
    $stmt->execute([$router['id']]);
    $localTickets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($localTickets as $localTicket) {
        if (!in_array($localTicket['mikrotik_id'], $mikrotikTicketsIds)) {
            // Le ticket n'existe plus sur le routeur, le marquer comme supprimé
            $stmt = $pdo->prepare("UPDATE tickets_internet SET statut = 'deleted' WHERE id = ?");
            $stmt->execute([$localTicket['id']]);
            $deleted++;
            
            logMessage("Ticket marqué comme supprimé", [
                'ticket_id' => $localTicket['id'],
                'mikrotik_id' => $localTicket['mikrotik_id']
            ]);
        }
    }
    
    // Valider toutes les modifications
    $pdo->commit();
    
    // Mettre à jour la date de dernière synchronisation du routeur
    $stmt = $pdo->prepare("UPDATE mikrotik_routers SET last_sync = NOW() WHERE id = ?");
    $stmt->execute([$router['id']]);
    
    // Déconnexion du routeur
    $mikrotik->disconnect();
    
    // Réponse de succès
    echo json_encode([
        'status' => 'success',
        'message' => 'Synchronisation terminée',
        'results' => [
            'added' => $added,
            'updated' => $updated,
            'deleted' => $deleted,
            'errors' => $errors
        ]
    ]);
    
} catch (Exception $e) {
    // Annuler la transaction en cas d'erreur
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    // Si le routeur était connecté, le marquer comme hors ligne
    if (isset($router) && isset($router['id'])) {
        try {
            $stmt = $pdo->prepare("UPDATE mikrotik_routers SET status = 'offline', last_sync = NOW() WHERE id = ?");
            $stmt->execute([$router['id']]);
        } catch (Exception $updateError) {
            logMessage("Impossible de mettre à jour le statut du routeur", [
                'error' => $updateError->getMessage()
            ], 'error');
        }
    }
    
    // Journaliser l'erreur
    logMessage("Erreur lors de la synchronisation avec Mikrotik", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], 'error');
    
    // Réponse d'erreur
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Erreur de synchronisation: ' . $e->getMessage()
    ]);
}
?>
