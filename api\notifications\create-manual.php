<?php
/**
 * create-manual.php
 * 
 * API endpoint pour créer une not
// Utiliser le système CORS centralisé
require_once __DIR__ . '/../cors.php';

ification manuellement depuis l'interface
 */

// Configuration pour assurer que les erreurs PHP sont retournées en JSON
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Inclusion de la fonction de log
require_once '../utils/logger.php';

// En-têtes CORS et JSON pour toutes les requêtes
// Retiré: // Retiré: // Commenté pour éviter duplication: // En-tête CORS géré par cors.php
// Retiré: // Retiré: // Commenté pour éviter duplication: header("Access-Control-Allow-Methods: POST, OPTIONS");
// Retiré: // Retiré: // Commenté pour éviter duplication: header("Access-Control-Max-Age: 3600");
// Retiré: // Retiré: // Commenté pour éviter duplication: header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Ajouter Content-Type uniquement pour les réponses non-OPTIONS
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    // Commenté pour éviter duplication: // Commenté pour éviter duplication: header("Content-Type: application/json; charset=UTF-8");
}

// Gestion spéciale pour les requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Vérifier si c'est une requête POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Méthode non autorisée. Utilisez POST.'
    ]);
    exit;
}

// Récupération des données
$jsonData = file_get_contents("php://input");
$data = json_decode($jsonData);

// Validation des données
if (!$data || !isset($data->ticket_id) || !isset($data->type)) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error', 
        'message' => 'Données incomplètes: ticket_id et type requis'
    ]);
    logMessage("Données incomplètes pour la création manuelle de notification", [
        'reçu' => json_decode($jsonData, true),
        'attendu' => 'ticket_id et type'
    ], 'error');
    exit;
}

try {
    // Connexion à la base de données
    require_once '../config.php';
    
    // Vérifier que le ticket existe
    $stmt = $pdo->prepare("
        SELECT t.* FROM tickets_internet t WHERE t.id = ?
    ");
    $stmt->execute([$data->ticket_id]);
    $ticket = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$ticket) {
        throw new Exception("Ticket non trouvé");
    }
    
    // Valider le type de notification
    $validTypes = ['ticket_first_connection', 'ticket_expiration', 'ticket_reminder'];
    $notificationType = $data->type;
    
    if (!in_array($notificationType, $validTypes)) {
        $notificationType = 'ticket_reminder'; // Type par défaut si invalide
    }
    
    // Préparer le message si non fourni
    $message = isset($data->message) ? $data->message : "";
    
    if (empty($message)) {
        switch ($notificationType) {
            case 'ticket_first_connection':
                $message = "Votre ticket WiFi (code: {$ticket['code']}) a été activé avec succès.";
                break;
            case 'ticket_expiration':
                $message = "Votre ticket WiFi (code: {$ticket['code']}) expire bientôt. Pensez à le renouveler.";
                break;
            case 'ticket_reminder':
                $message = "Rappel concernant votre ticket WiFi (code: {$ticket['code']}).";
                break;
        }
    }
    
    // Insérer la notification en base de données
    $stmt = $pdo->prepare("
        INSERT INTO notifications (
            ticket_id, type, message, status, metadata, created_at, updated_at
        ) VALUES (
            :ticket_id, :type, :message, 'pending', :metadata, NOW(), NOW()
        )
    ");
    $stmt->execute([
        ':ticket_id' => $data->ticket_id,
        ':type' => $notificationType,
        ':message' => $message,
        ':metadata' => json_encode(['manual' => true, 'created_by' => 'user_interface'])
    ]);
    
    $notificationId = $pdo->lastInsertId();
    
    logMessage("Notification manuelle créée", [
        'notification_id' => $notificationId,
        'ticket_id' => $data->ticket_id,
        'type' => $notificationType
    ]);
    
    // Réponse de succès
    echo json_encode([
        'status' => 'success',
        'message' => 'Notification créée avec succès',
        'notification_id' => $notificationId
    ]);
    
} catch (Exception $e) {
    // Journaliser l'erreur
    logMessage("Erreur lors de la création manuelle de notification", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'ticket_id' => isset($data->ticket_id) ? $data->ticket_id : 'unknown'
    ], 'error');
    
    // Réponse d'erreur
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Erreur lors de la création de la notification: ' . $e->getMessage()
    ]);
}
?>
