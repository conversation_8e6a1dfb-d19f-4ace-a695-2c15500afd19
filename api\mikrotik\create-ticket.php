<?php
/**
 * create-ticket.php
 * 
 * API endpoint pour créer un ticket Hotspot sur un routeur Mikrotik
 */

// Configuration pour assurer que les erreurs PHP sont retournées en JSON
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Inclusion de la fonction de log
require_once '../utils/logger.php';

// En-têtes CORS et JSON pour toutes les requêtes
require_once __DIR__ . '/../cors.php';
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Ajouter Content-Type uniquement pour les réponses non-OPTIONS
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    header("Content-Type: application/json; charset=UTF-8");
}

// Gestion spéciale pour les requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Vérifier si c'est une requête POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Méthode non autorisée. Utilisez POST.'
    ]);
    exit;
}

// Récupération des données
$jsonData = file_get_contents("php://input");
$data = json_decode($jsonData);

// Validation des données
if (!$data || !isset($data->router_id) || !isset($data->profile)) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error', 
        'message' => 'Données incomplètes: router_id et profile requis'
    ]);
    logMessage("Données incomplètes pour la création de ticket", [
        'reçu' => json_decode($jsonData, true),
        'attendu' => 'router_id et profile'
    ], 'error');
    exit;
}

try {
    // Connexion à la base de données
    require_once '../config.php';
    
    // Récupérer les informations du routeur
    $stmt = $pdo->prepare("SELECT * FROM mikrotik_routers WHERE id = ?");
    $stmt->execute([$data->router_id]);
    $router = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$router) {
        throw new Exception("Routeur non trouvé");
    }
    
    // Connexion à l'API Mikrotik via la bibliothèque RouterOS API
    require_once '../lib/routeros_api.class.php';
    $mikrotik = new RouterOS\API([
        'host' => $router['address'],
        'user' => $router['username'],
        'pass' => $router['password'],
        'port' => $router['port'],
    ]);
    
    // Tester la connexion avant de continuer
    if (!$mikrotik->connect()) {
        throw new Exception("Impossible de se connecter au routeur Mikrotik");
    }
    
    logMessage("Connexion établie avec le routeur Mikrotik pour création de ticket", [
        'router_id' => $router['id'],
        'name' => $router['name']
    ]);
    
    // Générer un nom d'utilisateur et mot de passe si non fournis
    $username = isset($data->username) ? $data->username : generateRandomCode(6);
    $password = isset($data->password) ? $data->password : generateRandomCode(6);
    
    // Créer un nouveau ticket hotspot
    $ticketRequest = new RouterOS\Request('/ip/hotspot/user/add');
    $ticketRequest->setArgument('name', $username);
    $ticketRequest->setArgument('password', $password);
    $ticketRequest->setArgument('profile', $data->profile);
    
    // Ajouter un commentaire si fourni
    if (isset($data->comment)) {
        $ticketRequest->setArgument('comment', $data->comment);
    }
    
    // Envoyer la requête pour créer le ticket
    $response = $mikrotik->sendSync($ticketRequest);
    
    // Vérifier si la création a réussi
    if (isset($response[0]['.id'])) {
        $mikrotikId = $response[0]['.id'];
        
        // Récupérer les détails du ticket créé
        $getTicket = new RouterOS\Request('/ip/hotspot/user/print');
        $getTicket->setQuery(RouterOS\Query::where('.id', $mikrotikId));
        $ticketDetails = $mikrotik->sendSync($getTicket);
        
        // Récupérer les informations de profil pour obtenir la durée de validité
        $getProfile = new RouterOS\Request('/ip/hotspot/user/profile/print');
        $getProfile->setQuery(RouterOS\Query::where('name', $data->profile));
        $profileDetails = $mikrotik->sendSync($getProfile);
        
        // Calculer la durée de validité et la date d'expiration
        $validity = '1d'; // Valeur par défaut: 1 jour
        $expirationDate = null;
        
        if (isset($profileDetails[0]['session-timeout'])) {
            $validity = $profileDetails[0]['session-timeout'];
            
            // Convertir la validité en timestamp pour calculer la date d'expiration
            // Format typique: 1d, 2h, 30m, etc.
            preg_match('/^(\d+)([dhms])$/', $validity, $matches);
            if (count($matches) === 3) {
                $value = intval($matches[1]);
                $unit = $matches[2];
                
                $expirationDate = new DateTime();
                switch ($unit) {
                    case 'd':
                        $expirationDate->add(new DateInterval("P{$value}D"));
                        break;
                    case 'h':
                        $expirationDate->add(new DateInterval("PT{$value}H"));
                        break;
                    case 'm':
                        $expirationDate->add(new DateInterval("PT{$value}M"));
                        break;
                    case 's':
                        $expirationDate->add(new DateInterval("PT{$value}S"));
                        break;
                }
            }
        }
        
        // Déterminer le type de ticket en fonction du profil
        $stmt = $pdo->prepare("SELECT * FROM ticket_types WHERE mikrotik_profile = ? LIMIT 1");
        $stmt->execute([$data->profile]);
        $ticketType = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Si aucun type correspondant n'est trouvé, utiliser le type par défaut
        if (!$ticketType) {
            $stmt = $pdo->prepare("SELECT * FROM ticket_types WHERE is_default = 1 LIMIT 1");
            $stmt->execute();
            $ticketType = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        // Enregistrer le ticket dans la base de données
        $stmt = $pdo->prepare("
            INSERT INTO tickets_internet 
            (mikrotik_id, mikrotik_router_id, code, type_ticket_id, libelle, prix, statut, 
             nom_client, date_creation, date_activation, date_expiration)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), ?)
        ");
        $stmt->execute([
            $mikrotikId,
            $router['id'],
            $username,
            $ticketType ? $ticketType['id'] : null,
            $ticketType ? $ticketType['label'] : 'Ticket Hotspot',
            $ticketType ? $ticketType['prix'] : 0,
            'active',
            $data->comment ?? 'Client Hotspot',
            $expirationDate ? $expirationDate->format('Y-m-d H:i:s') : null
        ]);
        
        $ticketId = $pdo->lastInsertId();
        
        // Déconnexion du routeur
        $mikrotik->disconnect();
        
        // Réponse de succès
        echo json_encode([
            'status' => 'success',
            'message' => 'Ticket créé avec succès',
            'ticket' => [
                'id' => $ticketId,
                'mikrotik_id' => $mikrotikId,
                'username' => $username,
                'password' => $password,
                'profile' => $data->profile,
                'validity' => $validity,
                'expiration' => $expirationDate ? $expirationDate->format('Y-m-d H:i:s') : null
            ]
        ]);
        
        logMessage("Ticket Hotspot créé avec succès", [
            'ticket_id' => $ticketId,
            'mikrotik_id' => $mikrotikId,
            'username' => $username
        ]);
    } else {
        throw new Exception("Échec de création du ticket Hotspot");
    }
    
} catch (Exception $e) {
    // Journaliser l'erreur
    logMessage("Erreur lors de la création du ticket Hotspot", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], 'error');
    
    // Réponse d'erreur
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Erreur de création de ticket: ' . $e->getMessage()
    ]);
}

/**
 * Génère un code aléatoire alphanumérique
 * @param int $length Longueur du code
 * @return string Code généré
 */
function generateRandomCode($length = 6) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $code = '';
    $max = strlen($characters) - 1;
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[mt_rand(0, $max)];
    }
    return $code;
}
?>
