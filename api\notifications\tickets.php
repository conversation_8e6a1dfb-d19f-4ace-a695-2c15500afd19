<?php
/**
 * tickets.php
 * 
 * API endpoint pour gérer les not
// Utiliser le système CORS centralisé
require_once __DIR__ . '/../cors.php';

ifications liées aux tickets Mikrotik
 */

// Configuration pour assurer que les erreurs PHP sont retournées en JSON
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Inclusion de la fonction de log
require_once '../utils/logger.php';

// En-têtes CORS et JSON pour toutes les requêtes
// Retiré: // Retiré: // Commenté pour éviter duplication: // En-tête CORS géré par cors.php
// Retiré: // Retiré: // Commenté pour éviter duplication: header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
// Retiré: // Retiré: // Commenté pour éviter duplication: header("Access-Control-Max-Age: 3600");
// Retiré: // Retiré: // Commenté pour éviter duplication: header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Ajouter Content-Type uniquement pour les réponses non-OPTIONS
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    // Commenté pour éviter duplication: // Commenté pour éviter duplication: header("Content-Type: application/json; charset=UTF-8");
}

// Gestion spéciale pour les requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Connexion à la base de données
require_once '../config.php';

// Traiter selon la méthode HTTP
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        // Récupérer les notifications de tickets
        $stmt = $pdo->prepare("
            SELECT n.*, t.code, t.libelle as type, t.nom_client as clientNom, 
                   t.email_client as clientEmail, t.telephone_client as clientTelephone,
                   t.date_creation, t.date_activation, t.date_expiration
            FROM notifications n
            JOIN tickets_internet t ON n.ticket_id = t.id
            WHERE n.type IN ('ticket_first_connection', 'ticket_expiration', 'ticket_reminder')
            ORDER BY n.created_at DESC
            LIMIT 100
        ");
        $stmt->execute();
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Transformation des données pour le format attendu par le client
        $formattedNotifications = [];
        foreach ($notifications as $notification) {
            $notificationType = 'reminder';
            if ($notification['type'] === 'ticket_first_connection') {
                $notificationType = 'first_connection';
            } else if ($notification['type'] === 'ticket_expiration') {
                $notificationType = 'expiration';
            }
            
            $formattedNotifications[] = [
                'id' => $notification['id'],
                'code' => $notification['code'],
                'type' => $notification['type'],
                'clientNom' => $notification['clientNom'],
                'clientEmail' => $notification['clientEmail'],
                'clientTelephone' => $notification['clientTelephone'],
                'dateCreation' => $notification['date_creation'],
                'dateActivation' => $notification['date_activation'],
                'dateExpiration' => $notification['date_expiration'],
                'status' => $notification['status'],
                'notificationType' => $notificationType,
                'message' => $notification['message']
            ];
        }
        
        // Réponse de succès
        echo json_encode([
            'status' => 'success',
            'notifications' => $formattedNotifications
        ]);
        
    } catch (Exception $e) {
        // Journaliser l'erreur
        logMessage("Erreur lors de la récupération des notifications de tickets", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ], 'error');
        
        // Réponse d'erreur
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => 'Erreur de récupération des notifications: ' . $e->getMessage()
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode([
        'status' => 'error', 
        'message' => 'Méthode non autorisée pour ce endpoint'
    ]);
}
?>
