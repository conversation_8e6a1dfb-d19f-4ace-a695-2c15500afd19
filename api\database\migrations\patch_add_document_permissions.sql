-- ======================================================================
-- PATCH POUR AJOUTER LES PERMISSIONS DE GESTION DES DOCUMENTS
-- Exécutez ce script pour mettre à jour la base de données existante
-- sans perdre de données.
-- ======================================================================

USE gestion_moulin_db;

-- 1. Ajouter les nouvelles permissions si elles n'existent pas
INSERT IGNORE INTO `permissions` (`name`, `description`) VALUES
('create_document', 'Ajouter des documents'),
('delete_document', 'Supprimer des documents');

-- 2. Attribuer les nouvelles permissions aux rôles concernés

-- Le rôle 'admin' a déjà toutes les permissions via une requête globale,
-- mais nous nous assurons qu'il les a bien en cas d'exécution partielle.
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 
    (SELECT id FROM `roles` WHERE `name` = 'admin'),
    p.id 
FROM `permissions` p 
WHERE p.name IN ('create_document', 'delete_document');

-- Attribuer les permissions au 'superviseur'
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 
    (SELECT id FROM `roles` WHERE `name` = 'superviseur'),
    p.id 
FROM `permissions` p 
WHERE p.name IN ('create_document', 'delete_document');

-- Attribuer les permissions au 'gerant'
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 
    (SELECT id FROM `roles` WHERE `name` = 'gerant'),
    p.id 
FROM `permissions` p 
WHERE p.name IN ('create_document', 'delete_document');

