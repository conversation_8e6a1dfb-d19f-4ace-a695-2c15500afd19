<?php
/**
 * sms_sender.php
 * 
 * Bibliothèque pour l'envoi de SMS aux clients
 * Utilisée par le système de notifications pour les tickets Mikrotik
 */

// Inclusion de la fonction de log
require_once __DIR__ . '/../utils/logger.php';

/**
 * Envoie un SMS à un numéro de téléphone
 * 
 * @param string $phoneNumber Numéro de téléphone du destinataire
 * @param string $message Contenu du SMS
 * @param array $options Options supplémentaires
 * @return bool True si le SMS a été envoyé avec succès, False sinon
 */
function sendSms($phoneNumber, $message, $options = []) {
    // Nettoyer le numéro de téléphone (enlever les espaces, tirets, etc.)
    $cleanedPhone = preg_replace('/[^0-9+]/', '', $phoneNumber);
    
    // Vérifier que le numéro de téléphone est valide (simple vérification)
    if (strlen($cleanedPhone) < 10) {
        logMessage("Numéro de téléphone invalide", ['phone' => $phoneNumber], 'error');
        return false;
    }
    
    try {
        // Lire la configuration depuis le fichier config ou utiliser les valeurs par défaut
        $configFile = __DIR__ . '/../config/sms_config.php';
        $smsProvider = 'log'; // Par défaut, juste logger les SMS sans les envoyer réellement
        $apiKey = '';
        $senderId = 'MoulinWifi';
        
        // Charger la configuration si elle existe
        if (file_exists($configFile)) {
            include $configFile;
            $smsProvider = defined('SMS_PROVIDER') ? SMS_PROVIDER : $smsProvider;
            $apiKey = defined('SMS_API_KEY') ? SMS_API_KEY : $apiKey;
            $senderId = defined('SMS_SENDER_ID') ? SMS_SENDER_ID : $senderId;
        }
        
        // Limiter la taille du message à 160 caractères (standard SMS)
        if (strlen($message) > 160) {
            $message = substr($message, 0, 157) . '...';
        }
        
        // Sélection du fournisseur SMS
        switch ($smsProvider) {
            case 'twilio':
                return sendSmsViaTwilio($cleanedPhone, $message, $apiKey, $options);
                
            case 'nexmo':
                return sendSmsViaNexmo($cleanedPhone, $message, $apiKey, $senderId, $options);
                
            case 'log':
            default:
                // Mode de développement : juste logger le SMS
                logMessage("SMS (simulation d'envoi)", [
                    'to' => $cleanedPhone,
                    'message' => $message,
                    'provider' => 'log'
                ]);
                return true;
        }
    } catch (Exception $e) {
        logMessage("Exception lors de l'envoi de SMS", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'phone' => $phoneNumber
        ], 'error');
        return false;
    }
}

/**
 * Envoie un SMS via l'API Twilio
 * 
 * @param string $phoneNumber Numéro de téléphone nettoyé
 * @param string $message Contenu du SMS
 * @param string $apiKey Clé API Twilio
 * @param array $options Options supplémentaires
 * @return bool True si le SMS a été envoyé avec succès, False sinon
 */
function sendSmsViaTwilio($phoneNumber, $message, $apiKey, $options = []) {
    // Vérifier que les configurations nécessaires sont disponibles
    if (empty($apiKey) || !isset($options['twilio_sid']) || !isset($options['twilio_from'])) {
        logMessage("Configuration Twilio incomplète", [], 'error');
        return false;
    }
    
    try {
        $sid = $options['twilio_sid'];
        $from = $options['twilio_from'];
        $url = "https://api.twilio.com/2010-04-01/Accounts/{$sid}/Messages.json";
        
        // Préparation des données pour l'API
        $data = [
            'From' => $from,
            'To' => $phoneNumber,
            'Body' => $message
        ];
        
        // Préparation de la requête cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_USERPWD, "{$sid}:{$apiKey}");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        // Exécution de la requête
        $response = curl_exec($ch);
        $info = curl_getinfo($ch);
        $error = curl_error($ch);
        curl_close($ch);
        
        // Vérifier la réponse
        if ($info['http_code'] >= 200 && $info['http_code'] < 300 && $response) {
            $responseData = json_decode($response, true);
            logMessage("SMS envoyé via Twilio", [
                'to' => $phoneNumber,
                'status' => $responseData['status'] ?? 'unknown',
                'sid' => $responseData['sid'] ?? 'unknown'
            ]);
            return true;
        } else {
            logMessage("Échec de l'envoi de SMS via Twilio", [
                'error' => $error ?: 'HTTP code: ' . $info['http_code'],
                'response' => $response,
                'to' => $phoneNumber
            ], 'error');
            return false;
        }
    } catch (Exception $e) {
        logMessage("Exception lors de l'envoi via Twilio", [
            'message' => $e->getMessage(),
            'to' => $phoneNumber
        ], 'error');
        return false;
    }
}

/**
 * Envoie un SMS via l'API Nexmo (Vonage)
 * 
 * @param string $phoneNumber Numéro de téléphone nettoyé
 * @param string $message Contenu du SMS
 * @param string $apiKey Clé API Nexmo
 * @param string $senderId Identifiant de l'expéditeur
 * @param array $options Options supplémentaires
 * @return bool True si le SMS a été envoyé avec succès, False sinon
 */
function sendSmsViaNexmo($phoneNumber, $message, $apiKey, $senderId, $options = []) {
    // Vérifier que les configurations nécessaires sont disponibles
    if (empty($apiKey) || !isset($options['nexmo_secret'])) {
        logMessage("Configuration Nexmo incomplète", [], 'error');
        return false;
    }
    
    try {
        $secret = $options['nexmo_secret'];
        $url = "https://rest.nexmo.com/sms/json";
        
        // Préparation des données pour l'API
        $data = [
            'api_key' => $apiKey,
            'api_secret' => $secret,
            'from' => $senderId,
            'to' => $phoneNumber,
            'text' => $message,
            'type' => 'text'
        ];
        
        // Préparation de la requête cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        // Exécution de la requête
        $response = curl_exec($ch);
        $info = curl_getinfo($ch);
        $error = curl_error($ch);
        curl_close($ch);
        
        // Vérifier la réponse
        if ($info['http_code'] >= 200 && $info['http_code'] < 300 && $response) {
            $responseData = json_decode($response, true);
            
            if (isset($responseData['messages'][0]['status']) && $responseData['messages'][0]['status'] === '0') {
                logMessage("SMS envoyé via Nexmo", [
                    'to' => $phoneNumber,
                    'message_id' => $responseData['messages'][0]['message-id'] ?? 'unknown'
                ]);
                return true;
            } else {
                logMessage("Échec de l'envoi de SMS via Nexmo", [
                    'error' => $responseData['messages'][0]['error-text'] ?? 'Unknown error',
                    'to' => $phoneNumber
                ], 'error');
                return false;
            }
        } else {
            logMessage("Échec de l'envoi de SMS via Nexmo", [
                'error' => $error ?: 'HTTP code: ' . $info['http_code'],
                'response' => $response,
                'to' => $phoneNumber
            ], 'error');
            return false;
        }
    } catch (Exception $e) {
        logMessage("Exception lors de l'envoi via Nexmo", [
            'message' => $e->getMessage(),
            'to' => $phoneNumber
        ], 'error');
        return false;
    }
}
