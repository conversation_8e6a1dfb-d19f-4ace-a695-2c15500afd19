<?php
header('Content-Type: text/html; charset=utf-8');
require_once 'api/config.php';

echo "<h1>🔍 Diagnostic Manager Moulins</h1>";

try {
    $manager_id = 2; // ID du manager de test
    
    echo "<h2>1. Vérification Manager</h2>";
    $stmt = $pdo->prepare("SELECT id, username, nom FROM users WHERE id = ?");
    $stmt->execute([$manager_id]);
    $manager = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($manager) {
        echo "✅ Manager trouvé: {$manager['username']} (ID: {$manager['id']})<br>";
    } else {
        echo "❌ Manager ID $manager_id non trouvé<br>";
        exit;
    }
    
    echo "<h2>2. Vérification colonne manager_id dans gerants</h2>";
    $checkColumn = $pdo->query("SHOW COLUMNS FROM gerants LIKE 'manager_id'");
    $columnExists = $checkColumn->fetch(PDO::FETCH_ASSOC);
    
    if ($columnExists) {
        echo "✅ Colonne manager_id existe dans table gerants<br>";
    } else {
        echo "❌ Colonne manager_id manquante dans table gerants<br>";
        echo "<p><a href='/api/add_manager_relation.php'>Cliquez ici pour ajouter la relation</a></p>";
        exit;
    }
    
    echo "<h2>3. Gérants assignés au manager</h2>";
    $stmt = $pdo->prepare("SELECT id, nom, manager_id FROM gerants WHERE manager_id = ?");
    $stmt->execute([$manager_id]);
    $gerants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($gerants)) {
        echo "❌ Aucun gérant assigné au manager $manager_id<br>";
        
        // Vérifier s'il y a des gérants sans manager
        $stmt = $pdo->query("SELECT id, nom FROM gerants WHERE manager_id IS NULL LIMIT 3");
        $gerants_libres = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($gerants_libres)) {
            echo "<p>Gérants disponibles pour assignation:</p>";
            echo "<ul>";
            foreach ($gerants_libres as $gerant) {
                echo "<li>{$gerant['nom']} (ID: {$gerant['id']})</li>";
            }
            echo "</ul>";
            echo "<p><a href='/api/add_manager_relation.php'>Assigner des gérants au manager</a></p>";
        } else {
            echo "<p>Aucun gérant disponible. <a href='/fix_manager_relation.php'>Créer des gérants de test</a></p>";
        }
        exit;
    } else {
        echo "✅ Gérants assignés au manager:<br>";
        foreach ($gerants as $gerant) {
            echo "- {$gerant['nom']} (ID: {$gerant['id']})<br>";
        }
    }
    
    echo "<h2>4. Moulins assignés aux gérants</h2>";
    $gerant_ids = array_column($gerants, 'id');
    $placeholders = str_repeat('?,', count($gerant_ids) - 1) . '?';
    
    $sql = "SELECT m.id, m.nom, m.gerant_id, g.nom AS gerant_nom 
            FROM moulins m 
            INNER JOIN gerants g ON m.gerant_id = g.id 
            WHERE g.id IN ($placeholders)";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($gerant_ids);
    $moulins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($moulins)) {
        echo "❌ Aucun moulin assigné aux gérants du manager<br>";
        
        // Vérifier s'il y a des moulins sans gérant
        $stmt = $pdo->query("SELECT id, nom FROM moulins WHERE gerant_id IS NULL LIMIT 5");
        $moulins_libres = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($moulins_libres)) {
            echo "<p>Moulins disponibles pour assignation:</p>";
            echo "<ul>";
            foreach ($moulins_libres as $moulin) {
                echo "<li>{$moulin['nom']} (ID: {$moulin['id']})</li>";
            }
            echo "</ul>";
            echo "<p><a href='/fix_manager_relation.php'>Assigner des moulins aux gérants</a></p>";
        }
    } else {
        echo "✅ Moulins trouvés pour le manager:<br>";
        foreach ($moulins as $moulin) {
            echo "- {$moulin['nom']} (Gérant: {$moulin['gerant_nom']})<br>";
        }
    }
    
    echo "<h2>5. Test API</h2>";
    $api_url = "http://localhost:8080/api/moulins.php?manager_id=$manager_id";
    echo "<p>URL API: <a href='$api_url' target='_blank'>$api_url</a></p>";
    
    echo "<h2>6. Actions recommandées</h2>";
    if (empty($gerants)) {
        echo "<p>1. <a href='/api/add_manager_relation.php'>Configurer les relations manager-gérant</a></p>";
    }
    if (empty($moulins)) {
        echo "<p>2. <a href='/fix_manager_relation.php'>Assigner des moulins aux gérants</a></p>";
    }
    echo "<p>3. <a href='/api/dashboard_moulins_fixed.php'>Tester la page dashboard</a></p>";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "<br>";
    echo "Fichier: " . $e->getFile() . "<br>";
    echo "Ligne: " . $e->getLine() . "<br>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
ul { margin: 10px 0; }
</style>
