<?php
/**
 * generate.php
 * 
 * Script pour générer automatiquement les not
// Utiliser le système CORS centralisé
require_once __DIR__ . '/../cors.php';

ifications pour:
 * - Première connexion des tickets
 * - Expiration des tickets
 * 
 * Ce script peut être exécuté via un cron job ou manuellement
 */

// Configuration pour assurer que les erreurs PHP sont enregistrées
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Inclusion de la fonction de log
require_once __DIR__ . '/../utils/logger.php';

// Déterminer si le script est lancé en ligne de commande ou via HTTP
$isCLI = (php_sapi_name() === 'cli');

// Répondre en JSON si en HTTP
if (!$isCLI) {
    // Retiré: // Retiré: // Commenté pour éviter duplication: // En-tête CORS géré par cors.php
    // Commenté pour éviter duplication: // Commenté pour éviter duplication: header("Content-Type: application/json; charset=UTF-8");
}

// Connexion à la base de données
require_once __DIR__ . '/../config.php';

// Fonction pour générer une réponse
function respond($status, $message, $data = []) {
    global $isCLI;
    
    if ($isCLI) {
        echo $message . PHP_EOL;
        if (!empty($data)) {
            print_r($data);
        }
    } else {
        echo json_encode([
            'status' => $status,
            'message' => $message,
            'data' => $data
        ]);
    }
}

// Fonction pour générer les notifications de première connexion
function generateFirstConnectionNotifications($pdo) {
    $notificationsCount = 0;
    
    try {
        // Trouver les tickets qui ont été activés mais n'ont pas encore de notification de première connexion
        $stmt = $pdo->prepare("
            SELECT t.* 
            FROM tickets_internet t
            LEFT JOIN notifications n ON t.id = n.ticket_id AND n.type = 'ticket_first_connection'
            WHERE t.date_activation IS NOT NULL 
              AND n.id IS NULL
              AND t.nom_client IS NOT NULL
              AND (t.email_client IS NOT NULL OR t.telephone_client IS NOT NULL)
              AND t.status = 'active'
        ");
        $stmt->execute();
        $tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($tickets as $ticket) {
            // Construire le message de notification
            $message = "Bonjour {$ticket['nom_client']}, votre ticket WiFi (code: {$ticket['code']}) a été activé avec succès le " . 
                      date('d/m/Y', strtotime($ticket['date_activation'])) . 
                      ". Vous pouvez maintenant profiter de votre connexion internet.";
            
            // Insérer la notification
            $stmt = $pdo->prepare("
                INSERT INTO notifications (
                    ticket_id, type, message, status, created_at, updated_at
                ) VALUES (
                    :ticket_id, 'ticket_first_connection', :message, 'pending', NOW(), NOW()
                )
            ");
            $stmt->execute([
                ':ticket_id' => $ticket['id'],
                ':message' => $message
            ]);
            
            $notificationsCount++;
            
            logMessage("Notification de première connexion créée", [
                'ticket_id' => $ticket['id'],
                'code' => $ticket['code'],
                'client' => $ticket['nom_client']
            ]);
        }
        
        return $notificationsCount;
    } catch (Exception $e) {
        logMessage("Erreur lors de la génération des notifications de première connexion", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ], 'error');
        
        throw $e;
    }
}

// Fonction pour générer les notifications d'expiration de ticket
function generateExpirationNotifications($pdo) {
    $notificationsCount = 0;
    
    try {
        // Jours d'avance pour avertir de l'expiration (1, 3 et 7 jours avant)
        $reminderDays = [1, 3, 7];
        
        foreach ($reminderDays as $days) {
            // Trouver les tickets qui expirent dans X jours et n'ont pas encore de notification
            $stmt = $pdo->prepare("
                SELECT t.* 
                FROM tickets_internet t
                LEFT JOIN notifications n ON t.id = n.ticket_id AND n.type = 'ticket_expiration' 
                    AND JSON_CONTAINS(n.metadata, :days_json, '$.days_before')
                WHERE t.date_expiration IS NOT NULL 
                  AND DATE(t.date_expiration) = DATE(DATE_ADD(CURRENT_DATE(), INTERVAL :days DAY))
                  AND n.id IS NULL
                  AND t.status = 'active'
                  AND t.nom_client IS NOT NULL
                  AND (t.email_client IS NOT NULL OR t.telephone_client IS NOT NULL)
            ");
            $stmt->execute([
                ':days' => $days,
                ':days_json' => json_encode($days)
            ]);
            $tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($tickets as $ticket) {
                // Construire le message de notification
                if ($days === 1) {
                    $message = "ATTENTION: Votre ticket WiFi (code: {$ticket['code']}) expire DEMAIN, le " . 
                              date('d/m/Y', strtotime($ticket['date_expiration'])) . 
                              ". Pensez à le renouveler pour éviter toute interruption de service.";
                } else {
                    $message = "Votre ticket WiFi (code: {$ticket['code']}) expire dans {$days} jours, le " . 
                              date('d/m/Y', strtotime($ticket['date_expiration'])) . 
                              ". Pensez à le renouveler avant cette date.";
                }
                
                // Insérer la notification avec des métadonnées pour éviter les doublons
                $stmt = $pdo->prepare("
                    INSERT INTO notifications (
                        ticket_id, type, message, status, metadata, created_at, updated_at
                    ) VALUES (
                        :ticket_id, 'ticket_expiration', :message, 'pending', :metadata, NOW(), NOW()
                    )
                ");
                $stmt->execute([
                    ':ticket_id' => $ticket['id'],
                    ':message' => $message,
                    ':metadata' => json_encode(['days_before' => $days])
                ]);
                
                $notificationsCount++;
                
                logMessage("Notification d'expiration créée", [
                    'ticket_id' => $ticket['id'],
                    'code' => $ticket['code'],
                    'client' => $ticket['nom_client'],
                    'days_before' => $days
                ]);
            }
        }
        
        return $notificationsCount;
    } catch (Exception $e) {
        logMessage("Erreur lors de la génération des notifications d'expiration", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ], 'error');
        
        throw $e;
    }
}

// Exécution principale
try {
    logMessage("Début de génération des notifications");
    
    // Générer les notifications de première connexion
    $firstConnectionCount = generateFirstConnectionNotifications($pdo);
    
    // Générer les notifications d'expiration
    $expirationCount = generateExpirationNotifications($pdo);
    
    // Résumé des résultats
    $totalCount = $firstConnectionCount + $expirationCount;
    $summary = [
        'first_connection' => $firstConnectionCount,
        'expiration' => $expirationCount,
        'total' => $totalCount
    ];
    
    logMessage("Fin de génération des notifications", $summary);
    
    if ($totalCount > 0) {
        respond('success', "Génération de notifications terminée: {$totalCount} notification(s) créée(s)", $summary);
    } else {
        respond('success', "Aucune nouvelle notification à générer", $summary);
    }
} catch (Exception $e) {
    logMessage("Erreur lors de la génération des notifications", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], 'error');
    
    respond('error', "Erreur: " . $e->getMessage());
}
?>
