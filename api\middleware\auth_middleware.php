<?php
/**
 * Middleware d'authentification pour vérifier les droits d'accès
 */
class AuthMiddleware {
    /**
     * Vérifie si l'utilisateur est connecté
     * @return bool
     */
    public function isAuthenticated() {
        // Vérifier le token d'authentification
        $headers = getallheaders();
        $authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
        
        if (empty($authHeader)) {
            return false;
        }
        
        // Format attendu: "Bearer [token]"
        $token = str_replace('Bearer ', '', $authHeader);
        
        if (empty($token)) {
            return false;
        }
        
        // Vérifier si le token existe dans la session
        session_start();
        if (isset($_SESSION['user_id']) && isset($_SESSION['token']) && $_SESSION['token'] === $token) {
            return true;
        }
        
        // Si pas en session, vérifier dans la base de données
        require_once __DIR__ . '/../config.php';
        global $pdo;
        
        try {
            $stmt = $pdo->prepare("
                SELECT id FROM users 
                WHERE auth_token = ? AND auth_token_expiry > NOW()
            ");
            $stmt->execute([$token]);
            
            return $stmt->fetch() !== false;
        } catch (PDOException $e) {
            error_log('Erreur d\'authentification: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Vérifie si l'utilisateur est un administrateur
     * @return bool
     */
    public function isAdmin() {
        // Vérifier d'abord si l'utilisateur est authentifié
        if (!$this->isAuthenticated()) {
            error_log("[AUTH_MIDDLEWARE] Utilisateur non authentifié lors de la vérification admin");
            return false;
        }
        
        $headers = getallheaders();
        $authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
        $token = str_replace('Bearer ', '', $authHeader);
        
        // Log pour le débogage
        error_log("[AUTH_MIDDLEWARE] Vérification admin avec token: " . substr($token, 0, 10) . "...");
        
        require_once __DIR__ . '/../config.php';
        global $pdo;
        
        try {
            // Récupérer les informations complètes de l'utilisateur
            $stmt = $pdo->prepare("
                SELECT id, username, role, auth_token_expiry 
                FROM users 
                WHERE auth_token = ? AND auth_token_expiry > NOW()
            ");
            $stmt->execute([$token]);
            
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Journaliser le résultat pour le débogage
            if ($user) {
                error_log("[AUTH_MIDDLEWARE] Utilisateur trouvé - ID: {$user['id']}, Role: {$user['role']}, Expiration: {$user['auth_token_expiry']}");
                return $user['role'] === 'admin';
            } else {
                error_log("[AUTH_MIDDLEWARE] Aucun utilisateur trouvé pour ce token ou token expiré");
                return false;
            }
        } catch (PDOException $e) {
            error_log('[AUTH_MIDDLEWARE] Erreur de vérification admin: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Récupère l'ID de l'utilisateur connecté
     * @return int|null
     */
    public function getUserId() {
        if (!$this->isAuthenticated()) {
            return null;
        }
        
        $headers = getallheaders();
        $authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
        $token = str_replace('Bearer ', '', $authHeader);
        
        require_once __DIR__ . '/../config.php';
        global $pdo;
        
        try {
            $stmt = $pdo->prepare("
                SELECT id FROM users 
                WHERE auth_token = ? AND auth_token_expiry > NOW()
            ");
            $stmt->execute([$token]);
            
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $user ? $user['id'] : null;
        } catch (PDOException $e) {
            error_log('Erreur de récupération user_id: ' . $e->getMessage());
            return null;
        }
    }
}
