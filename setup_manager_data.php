<?php
header('Content-Type: text/html; charset=utf-8');
require_once 'api/config.php';

echo "<h1>🔧 Configuration Manager-Gérant-Moulin</h1>";

try {
    $manager_id = 2; // ID du manager de test
    
    // 1. Vérifier/créer la colonne manager_id dans gerants
    echo "<h2>1. Configuration table gerants</h2>";
    $checkColumn = $pdo->query("SHOW COLUMNS FROM gerants LIKE 'manager_id'");
    $columnExists = $checkColumn->fetch(PDO::FETCH_ASSOC);
    
    if (!$columnExists) {
        echo "Ajout de la colonne manager_id...<br>";
        $pdo->exec("ALTER TABLE gerants ADD COLUMN manager_id INT(11) NULL");
        $pdo->exec("ALTER TABLE gerants ADD CONSTRAINT fk_gerant_manager FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL");
        echo "✅ Colonne manager_id ajoutée<br>";
    } else {
        echo "✅ Colonne manager_id existe déjà<br>";
    }
    
    // 2. Vérifier/créer des gérants de test
    echo "<h2>2. Configuration gérants</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM gerants");
    $gerant_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($gerant_count == 0) {
        echo "Création de gérants de test...<br>";
        
        // Créer des utilisateurs gérants d'abord
        $gerants_data = [
            ['nom' => 'Gérant Test 1', 'email' => '<EMAIL>', 'telephone' => '90000001'],
            ['nom' => 'Gérant Test 2', 'email' => '<EMAIL>', 'telephone' => '90000002'],
            ['nom' => 'Gérant Test 3', 'email' => '<EMAIL>', 'telephone' => '90000003']
        ];
        
        foreach ($gerants_data as $gerant) {
            // Vérifier si l'utilisateur existe déjà
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$gerant['email']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                // Créer l'utilisateur
                $stmt = $pdo->prepare("INSERT INTO users (username, nom, email, telephone, password, role_id, statut) VALUES (?, ?, ?, ?, ?, 3, 'Actif')");
                $username = strtolower(str_replace(' ', '', $gerant['nom']));
                $stmt->execute([$username, $gerant['nom'], $gerant['email'], $gerant['telephone'], password_hash('password123', PASSWORD_DEFAULT)]);
                $user_id = $pdo->lastInsertId();
            } else {
                $user_id = $user['id'];
            }
            
            // Créer le gérant
            $stmt = $pdo->prepare("INSERT INTO gerants (nom, telephone, email, user_id, manager_id, statut) VALUES (?, ?, ?, ?, ?, 'Actif')");
            $stmt->execute([$gerant['nom'], $gerant['telephone'], $gerant['email'], $user_id, $manager_id]);
            
            echo "✅ Gérant créé: {$gerant['nom']}<br>";
        }
    } else {
        echo "✅ $gerant_count gérants trouvés<br>";
        
        // Assigner les gérants au manager s'ils ne le sont pas déjà
        $stmt = $pdo->prepare("UPDATE gerants SET manager_id = ? WHERE manager_id IS NULL LIMIT 3");
        $stmt->execute([$manager_id]);
        $affected = $stmt->rowCount();
        if ($affected > 0) {
            echo "✅ $affected gérants assignés au manager<br>";
        }
    }
    
    // 3. Vérifier/créer des moulins de test
    echo "<h2>3. Configuration moulins</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM moulins");
    $moulin_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($moulin_count == 0) {
        echo "Création de moulins de test...<br>";
        
        $moulins_data = [
            ['nom' => 'Moulin Central', 'adresse' => 'Centre-ville, Lomé', 'type' => 'Electrique', 'puissance' => '15'],
            ['nom' => 'Moulin Nord', 'adresse' => 'Quartier Nord, Lomé', 'type' => 'Electrique', 'puissance' => '20'],
            ['nom' => 'Moulin Sud', 'adresse' => 'Quartier Sud, Lomé', 'type' => 'Mécanique', 'puissance' => '10'],
            ['nom' => 'Moulin Est', 'adresse' => 'Quartier Est, Lomé', 'type' => 'Hybride', 'puissance' => '25'],
            ['nom' => 'Moulin Ouest', 'adresse' => 'Quartier Ouest, Lomé', 'type' => 'Electrique', 'puissance' => '18']
        ];
        
        foreach ($moulins_data as $moulin) {
            $code = strtoupper(substr($moulin['nom'], 0, 3)) . date('ymd') . rand(10, 99);
            $stmt = $pdo->prepare("INSERT INTO moulins (nom, code, adresse, type, puissance, statut, cout_achat, date_installation) VALUES (?, ?, ?, ?, ?, 'actif', 500000, CURDATE())");
            $stmt->execute([$moulin['nom'], $code, $moulin['adresse'], $moulin['type'], $moulin['puissance']]);
            
            echo "✅ Moulin créé: {$moulin['nom']}<br>";
        }
    } else {
        echo "✅ $moulin_count moulins trouvés<br>";
    }
    
    // 4. Assigner les moulins aux gérants
    echo "<h2>4. Assignation moulins aux gérants</h2>";
    
    // Récupérer les gérants du manager
    $stmt = $pdo->prepare("SELECT id, nom FROM gerants WHERE manager_id = ?");
    $stmt->execute([$manager_id]);
    $gerants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Récupérer les moulins non assignés
    $stmt = $pdo->query("SELECT id, nom FROM moulins WHERE gerant_id IS NULL");
    $moulins_libres = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($gerants) && !empty($moulins_libres)) {
        $gerant_index = 0;
        foreach ($moulins_libres as $moulin) {
            $gerant = $gerants[$gerant_index % count($gerants)];
            
            $stmt = $pdo->prepare("UPDATE moulins SET gerant_id = ? WHERE id = ?");
            $stmt->execute([$gerant['id'], $moulin['id']]);
            
            echo "✅ Moulin '{$moulin['nom']}' assigné au gérant '{$gerant['nom']}'<br>";
            $gerant_index++;
        }
    }
    
    // 5. Résumé final
    echo "<h2>5. Résumé final</h2>";
    
    $stmt = $pdo->prepare("
        SELECT m.nom as moulin_nom, g.nom as gerant_nom 
        FROM moulins m 
        INNER JOIN gerants g ON m.gerant_id = g.id 
        WHERE g.manager_id = ?
    ");
    $stmt->execute([$manager_id]);
    $moulins_manager = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>✅ Configuration terminée !</p>";
    echo "<p>Moulins visibles par le manager (ID: $manager_id):</p>";
    echo "<ul>";
    foreach ($moulins_manager as $moulin) {
        echo "<li>{$moulin['moulin_nom']} - Gérant: {$moulin['gerant_nom']}</li>";
    }
    echo "</ul>";
    
    echo "<h2>6. Tests</h2>";
    echo "<p><a href='/api/moulins.php?manager_id=$manager_id' target='_blank'>Tester l'API moulins</a></p>";
    echo "<p><a href='/api/dashboard_moulins_fixed.php' target='_blank'>Tester le dashboard</a></p>";
    echo "<p><a href='/manager/dashboard/moulins' target='_blank'>Accéder au dashboard manager</a></p>";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "<br>";
    echo "Fichier: " . $e->getFile() . "<br>";
    echo "Ligne: " . $e->getLine() . "<br>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
a { color: #007cba; text-decoration: none; padding: 5px 10px; background: #f0f0f0; border-radius: 3px; }
a:hover { background: #e0e0e0; }
ul { margin: 10px 0; }
</style>
