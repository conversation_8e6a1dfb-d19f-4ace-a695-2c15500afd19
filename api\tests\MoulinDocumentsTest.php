<?php

use PHPUnit\Framework\TestCase;

/**
 * Tests fonctionnels pour l'ajout de documents associés aux moulins
 */
class MoulinDocumentsTest extends TestCase
{
    private $pdo;
    private $moulinId;
    private $documentTypeId;
    private $uploadDir;
    
    protected function setUp(): void
    {
        // Initialiser la connexion à la base de données de test
        $this->pdo = getTestDbConnection();
        
        // Créer le répertoire d'upload pour les tests s'il n'existe pas
        $this->uploadDir = dirname(__DIR__) . '/uploads/test';
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0777, true);
        }
        
        // Créer un moulin de test
        $stmt = $this->pdo->prepare("
            INSERT INTO moulins (code, nom, ville, pays, statut)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            'TEST' . time(),
            'Moulin Test Documents',
            'Ville Test',
            'Togo',
            'actif'
        ]);
        
        $this->moulinId = $this->pdo->lastInsertId();
        
        // Récupérer un type de document existant pour les tests
        $stmt = $this->pdo->prepare("SELECT id FROM document_types WHERE nom = ? LIMIT 1");
        $stmt->execute(['facture']);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            $this->documentTypeId = $result['id'];
        } else {
            // Créer un type de document si aucun n'existe
            $stmt = $this->pdo->prepare("
                INSERT INTO document_types (nom, description)
                VALUES (?, ?)
            ");
            $stmt->execute(['facture', 'Factures de test']);
            $this->documentTypeId = $this->pdo->lastInsertId();
        }
    }
    
    /**
     * Test d'ajout d'un document à un moulin
     */
    public function testAjoutDocument()
    {
        // Simuler un fichier uploadé
        $fileName = 'test_document_' . time() . '.pdf';
        $filePath = $this->uploadDir . '/' . $fileName;
        
        // Créer un fichier PDF de test
        $this->createTestPdf($filePath);
        
        // Vérifier que le fichier a bien été créé
        $this->assertFileExists($filePath);
        
        // Appeler la fonction d'ajout de document
        require_once dirname(__DIR__) . '/fonctions/moulin_documents.php';
        
        $documentData = [
            'moulin_id' => $this->moulinId,
            'document_type_id' => $this->documentTypeId,
            'titre' => 'Document Test',
            'description' => 'Description du document de test',
            'chemin_fichier' => $filePath
        ];
        
        // Test de la fonction d'ajout de document
        $documentId = ajouterDocumentMoulin($documentData);
        
        // Vérifier que le document a bien été ajouté en base de données
        $this->assertIsNumeric($documentId);
        $this->assertGreaterThan(0, $documentId);
        
        // Vérifier que le document est correctement associé au moulin
        $stmt = $this->pdo->prepare("
            SELECT * FROM moulin_documents
            WHERE id = ?
        ");
        $stmt->execute([$documentId]);
        $document = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals($this->moulinId, $document['moulin_id']);
        $this->assertEquals($this->documentTypeId, $document['document_type_id']);
        $this->assertEquals('Document Test', $document['titre']);
    }
    
    /**
     * Test de récupération des documents d'un moulin
     */
    public function testRecuperationDocuments()
    {
        // Ajouter plusieurs documents pour le test
        for ($i = 1; $i <= 3; $i++) {
            // Simuler un fichier uploadé
            $fileName = 'test_document_' . time() . '_' . $i . '.pdf';
            $filePath = $this->uploadDir . '/' . $fileName;
            
            // Créer un fichier PDF de test
            $this->createTestPdf($filePath);
            
            // Insérer le document en base de données
            $stmt = $this->pdo->prepare("
                INSERT INTO moulin_documents 
                (moulin_id, document_type_id, titre, chemin_fichier, description)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $this->moulinId,
                $this->documentTypeId,
                'Document Test ' . $i,
                $filePath,
                'Description du document de test ' . $i
            ]);
        }
        
        // Appeler la fonction de récupération des documents
        require_once dirname(__DIR__) . '/fonctions/moulin_documents.php';
        $documents = recupererDocumentsMoulin($this->moulinId);
        
        // Vérifier que la liste des documents est correcte
        $this->assertIsArray($documents);
        $this->assertCount(3, $documents);
        
        // Vérifier le contenu des documents récupérés
        foreach ($documents as $document) {
            $this->assertEquals($this->moulinId, $document['moulin_id']);
            $this->assertStringStartsWith('Document Test', $document['titre']);
            $this->assertFileExists($document['chemin_fichier']);
        }
    }
    
    /**
     * Test de suppression d'un document
     */
    public function testSuppressionDocument()
    {
        // Ajouter un document pour le test de suppression
        $fileName = 'test_document_to_delete_' . time() . '.pdf';
        $filePath = $this->uploadDir . '/' . $fileName;
        
        // Créer un fichier PDF de test
        $this->createTestPdf($filePath);
        
        // Insérer le document en base de données
        $stmt = $this->pdo->prepare("
            INSERT INTO moulin_documents 
            (moulin_id, document_type_id, titre, chemin_fichier, description)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $this->moulinId,
            $this->documentTypeId,
            'Document à Supprimer',
            $filePath,
            'Description du document à supprimer'
        ]);
        
        $documentId = $this->pdo->lastInsertId();
        
        // Appeler la fonction de suppression
        require_once dirname(__DIR__) . '/fonctions/moulin_documents.php';
        $resultat = supprimerDocumentMoulin($documentId);
        
        // Vérifier que le document a été supprimé
        $this->assertTrue($resultat);
        
        // Vérifier que le document n'existe plus en base de données
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM moulin_documents WHERE id = ?");
        $stmt->execute([$documentId]);
        $count = $stmt->fetchColumn();
        
        $this->assertEquals(0, $count);
        
        // Vérifier que le fichier a également été supprimé
        $this->assertFileDoesNotExist($filePath);
    }
    
    /**
     * Test d'upload d'un fichier et association à un moulin
     */
    public function testUploadEtAssociationDocument()
    {
        // Simuler un fichier uploadé
        $tempFile = tempnam(sys_get_temp_dir(), 'test');
        file_put_contents($tempFile, 'Contenu du fichier de test');
        
        $uploadedFile = [
            'name' => 'document_upload_test.txt',
            'type' => 'text/plain',
            'tmp_name' => $tempFile,
            'error' => UPLOAD_ERR_OK,
            'size' => filesize($tempFile)
        ];
        
        $documentData = [
            'moulin_id' => $this->moulinId,
            'document_type_id' => $this->documentTypeId,
            'titre' => 'Document Uploadé',
            'description' => 'Document téléchargé via le formulaire'
        ];
        
        // Appeler la fonction d'upload et association
        require_once dirname(__DIR__) . '/fonctions/moulin_documents.php';
        $resultat = uploadEtAssocierDocument($uploadedFile, $documentData, $this->uploadDir);
        
        // Vérifier que l'upload et l'association ont réussi
        $this->assertIsArray($resultat);
        $this->assertTrue($resultat['success']);
        $this->assertIsNumeric($resultat['document_id']);
        
        // Vérifier que le document existe en base
        $stmt = $this->pdo->prepare("SELECT * FROM moulin_documents WHERE id = ?");
        $stmt->execute([$resultat['document_id']]);
        $document = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertNotFalse($document);
        $this->assertEquals($this->moulinId, $document['moulin_id']);
        $this->assertEquals('Document Uploadé', $document['titre']);
        
        // Vérifier que le fichier a été correctement enregistré
        $this->assertFileExists($document['chemin_fichier']);
    }
    
    /**
     * Méthode utilitaire pour créer un fichier PDF de test
     */
    private function createTestPdf($filePath)
    {
        // Contenu minimal d'un PDF
        $pdfContent = "%PDF-1.4\n"
            . "1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj\n"
            . "2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj\n"
            . "3 0 obj<</Type/Page/MediaBox[0 0 595 842]/Parent 2 0 R/Resources<<>>>>endobj\n"
            . "xref\n"
            . "0 4\n"
            . "0000000000 65535 f\n"
            . "0000000010 00000 n\n"
            . "0000000053 00000 n\n"
            . "0000000102 00000 n\n"
            . "trailer<</Size 4/Root 1 0 R>>\n"
            . "startxref\n"
            . "183\n"
            . "%%EOF";
        
        file_put_contents($filePath, $pdfContent);
    }
    
    protected function tearDown(): void
    {
        // Supprimer les documents de test
        $stmt = $this->pdo->prepare("SELECT chemin_fichier FROM moulin_documents WHERE moulin_id = ?");
        $stmt->execute([$this->moulinId]);
        $documents = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($documents as $document) {
            if (file_exists($document['chemin_fichier'])) {
                unlink($document['chemin_fichier']);
            }
        }
        
        // Supprimer les enregistrements de la base de données
        $this->pdo->exec("DELETE FROM moulin_documents WHERE moulin_id = {$this->moulinId}");
        $this->pdo->exec("DELETE FROM moulins WHERE id = {$this->moulinId}");
        
        // Nettoyer les variables
        $this->pdo = null;
    }
}
