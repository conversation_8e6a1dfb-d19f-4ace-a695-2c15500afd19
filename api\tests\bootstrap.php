<?php

// Chemin vers l'autoloader si vous utilisez composer
// Si vous n'avez pas d'autoloader, vous pouvez inclure directement les fichiers nécessaires ici
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once __DIR__ . '/../vendor/autoload.php';
}

// Définir les constantes nécessaires pour l'environnement de test
define('TEST_MODE', true);

// Connexion à la base de données de test
function getTestDbConnection() {
    $host = 'localhost';
    $dbname = 'gestion_moulin_test';
    $username = 'root';
    $password = '';
    
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        echo "Erreur de connexion à la base de données: " . $e->getMessage();
        exit;
    }
}

// Fonction pour initialiser la base de données de test avec des données minimales
function setupTestDatabase() {
    $pdo = getTestDbConnection();
    
    // Effacer toutes les tables existantes et recréer la structure
    $sql = file_get_contents(__DIR__ . '/../complete_database_setup.sql');
    $pdo->exec($sql);
    
    // Insérer des données de test
    $sql = "INSERT INTO users (username, password, email, role, active)
            VALUES ('testuser', '".password_hash('password123', PASSWORD_DEFAULT)."', '<EMAIL>', 'admin', 1)";
    $pdo->exec($sql);
}

// Décommenter pour réinitialiser la base de données avant les tests
// setupTestDatabase();
