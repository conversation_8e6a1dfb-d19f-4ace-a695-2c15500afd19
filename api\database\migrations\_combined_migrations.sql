-- ======================================================================
-- SCRIPT DE MIGRATION COMBINÉ
-- Gén<PERSON><PERSON> le: 2025-07-10
-- Ce fichier contient l'ensemble des migrations de la base de données.
-- Exécutez ce script pour initialiser ou réinitialiser complètement la base.
-- ======================================================================

-- ======================================================================
-- Fichier: 01_users_auth_setup.sql
-- ======================================================================

-- Module Utilisateurs et Authentification
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des rôles
CREATE TABLE IF NOT EXISTS `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertion des rôles par défaut
INSERT IGNORE INTO `roles` (`name`, `description`) VALUES
('admin', 'Administrateur avec accès complet'),
('superviseur', 'Superviseur avec accès à la gestion globale'),
('gerant', 'Gérant de moulin'),
('vendeur', 'Vendeur de tickets'),
('agent_transfert', 'Agent pour les opérations de transfert');

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `nom` VARCHAR(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `telephone` VARCHAR(20) DEFAULT NULL,
  `role_id` int(11) NOT NULL,
  `statut` VARCHAR(50) NOT NULL DEFAULT 'Actif',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `last_login` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Création d'un utilisateur admin par défaut (mot de passe: admin123)
INSERT IGNORE INTO `users` (`username`, `password`, `email`, `role_id`, `statut`, `created_at`)
VALUES ('admin', '$2y$10$vuwu8ZDlXiHgKhkOBYpK8OdPuwB26aRA5jWys.DDCTc4zPGmpUgd', '<EMAIL>', (SELECT id FROM roles WHERE name = 'admin'), 'Actif', NOW());

-- Création d'un utilisateur gérant de test (mot de passe: manager123)
INSERT IGNORE INTO `users` (`username`, `password`, `email`, `role_id`, `statut`, `created_at`)
VALUES ('manager', '$2y$10$ZYtSOBAttu7IOY1RPRtyGepkeGQQQjkSVM.c2Vqk8QJkqNwQmQZp', '<EMAIL>', (SELECT id FROM roles WHERE name = 'gerant'), 'Actif', NOW());

-- Table des permissions
CREATE TABLE IF NOT EXISTS `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertion des permissions par défaut
INSERT IGNORE INTO `permissions` (`name`, `description`) VALUES
('manage_users', 'Gérer les utilisateurs'),
('view_reports', 'Voir les rapports'),
('manage_moulins', 'Gérer les moulins'),
('manage_gerants', 'Gérer les gérants'),
('sell_tickets', 'Vendre des tickets'),
('manage_finances', 'Gérer les finances'),
('manage_alerts', 'Gérer les alertes'),
('manage_transferts', 'Gérer les transferts de fonds'),
('create_document', 'Ajouter des documents'),
('delete_document', 'Supprimer des documents');

-- Table de liaison rôles-permissions
CREATE TABLE IF NOT EXISTS `role_permissions` (
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`role_id`, `permission_id`),
  FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Attribution des permissions aux rôles
-- Admin a toutes les permissions
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id FROM `roles` r, `permissions` p WHERE r.name = 'admin';

-- Superviseur a la plupart des permissions sauf la gestion des utilisateurs
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id FROM `roles` r, `permissions` p 
WHERE r.name = 'superviseur' AND p.name != 'manage_users';

-- Gérant peut gérer son moulin, ses finances, les documents et voir les rapports
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id FROM `roles` r, `permissions` p 
WHERE r.name = 'gerant' AND p.name IN ('manage_moulins', 'manage_finances', 'view_reports', 'create_document', 'delete_document');

-- Vendeur peut vendre des tickets
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id FROM `roles` r, `permissions` p 
WHERE r.name = 'vendeur' AND p.name = 'sell_tickets';

-- Agent de transfert peut gérer les transferts
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id FROM `roles` r, `permissions` p 
WHERE r.name = 'agent_transfert' AND p.name = 'manage_transferts';

-- Table de l'historique de connexion
CREATE TABLE IF NOT EXISTS `login_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `login_time` datetime NOT NULL,
  `logout_time` datetime DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- ======================================================================
-- Fichier: 02_moulins_setup.sql
-- ======================================================================

-- Module Moulins
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des moulins
CREATE TABLE IF NOT EXISTS `moulins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(20) NOT NULL,
  `nom` varchar(100) NOT NULL,
  `adresse` varchar(255) DEFAULT NULL,
  `ville` varchar(100) NOT NULL,
  `pays` varchar(100) NOT NULL DEFAULT 'Togo',
  `coordonnees_gps` varchar(100) DEFAULT NULL,
  `type` varchar(100) DEFAULT NULL, -- Ajouté
  `puissance` varchar(50) DEFAULT NULL, -- Ajouté
  `date_installation` date DEFAULT NULL,
  `cout_achat` DECIMAL(12, 2) DEFAULT NULL,
  `statut` enum('actif','maintenance','hors_service') NOT NULL DEFAULT 'actif',
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des types de documents
CREATE TABLE IF NOT EXISTS `document_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(50) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nom` (`nom`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des documents des moulins
CREATE TABLE IF NOT EXISTS `moulin_documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `moulin_id` int(11) NOT NULL,
  `document_type_id` int(11) NOT NULL,
  `titre` varchar(100) NOT NULL,
  `chemin_fichier` varchar(255) NOT NULL,
  `date_ajout` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`document_type_id`) REFERENCES `document_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des événements des moulins
CREATE TABLE IF NOT EXISTS `moulin_evenements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `moulin_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `type` enum('maintenance','panne','reparation','autre') NOT NULL,
  `description` text NOT NULL,
  `date_debut` datetime NOT NULL,
  `date_fin` datetime DEFAULT NULL,
  `cout` decimal(10,2) DEFAULT NULL,
  `statut` enum('en_cours','termine','annule') NOT NULL DEFAULT 'en_cours',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertion des types de documents par défaut
INSERT IGNORE INTO `document_types` (`nom`, `description`) VALUES
('facture', 'Factures d''achat ou de service'),
('photo', 'Photos du moulin'),
('contrat', 'Contrats liés au moulin'),
('maintenance', 'Documents de maintenance'),
('autre', 'Autres types de documents');

-- Insertion d'un moulin de test
INSERT IGNORE INTO `moulins` (`code`, `nom`, `adresse`, `ville`, `pays`, `statut`, `description`, `date_installation`)
VALUES ('M001', 'Moulin Central', 'Rue du Commerce', 'Lomé', 'Togo', 'actif', 'Premier moulin de test', '2025-01-01');


-- ======================================================================
-- Fichier: 03_gerants_setup.sql
-- ======================================================================

-- Module Gérants
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des gérants (structure alignée avec gerants.php)
CREATE TABLE IF NOT EXISTS `gerants` (
  `id` VARCHAR(10) NOT NULL PRIMARY KEY,
  `user_id` INT(11) NOT NULL,
  `nom` VARCHAR(100) NOT NULL,
  `telephone` VARCHAR(20) NOT NULL,
  `email` VARCHAR(100) NOT NULL,
  `statut` VARCHAR(50) DEFAULT 'Actif',
  `dateEmbauche` DATE,
  `moulinsAssignes` JSON DEFAULT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des absences de gérants
CREATE TABLE IF NOT EXISTS `absences` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `gerant_id` VARCHAR(10) NOT NULL,
  `type` enum('conge','maladie','absence_justifiee','absence_non_justifiee') NOT NULL,
  `date_debut` date NOT NULL,
  `date_fin` date NOT NULL,
  `justification` text DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`gerant_id`) REFERENCES `gerants` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des performances des gérants
CREATE TABLE IF NOT EXISTS `performances` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `gerant_id` VARCHAR(10) NOT NULL,
  `moulin_id` INT(11) NOT NULL,
  `periode` varchar(7) NOT NULL COMMENT 'Format: YYYY-MM',
  `ventes_total` decimal(12,2) DEFAULT 0,
  `objectif_ventes` decimal(12,2) DEFAULT 0,
  `taux_realisation` decimal(5,2) DEFAULT 0,
  `evaluation` enum('excellent','bon','moyen','insuffisant') DEFAULT NULL,
  `commentaire` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `gerant_periode_moulin` (`gerant_id`, `periode`, `moulin_id`),
  FOREIGN KEY (`gerant_id`) REFERENCES `gerants` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- ======================================================================
-- Fichier: 04_tickets_mikrotik_setup.sql
-- ======================================================================

-- Module Vente de Tickets Mikrotik
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des clients
CREATE TABLE IF NOT EXISTS `clients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(100) NOT NULL,
  `prenom` varchar(100) DEFAULT NULL,
  `telephone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `adresse` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `telephone` (`telephone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des routeurs/hotspots Mikrotik
CREATE TABLE IF NOT EXISTS `mikrotik_hotspots` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `moulin_id` int(11) NOT NULL,
  `nom` varchar(100) NOT NULL,
  `adresse_ip` varchar(45) NOT NULL,
  `port` int(11) NOT NULL DEFAULT 8728,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `ssl` tinyint(1) NOT NULL DEFAULT 0,
  `actif` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des tickets Internet (révisé avec type)
CREATE TABLE IF NOT EXISTS `tickets_internet` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(20) NOT NULL,
  `type` varchar(50) NOT NULL DEFAULT 'Standard',
  `duree_validite` int(11) NOT NULL COMMENT 'Durée en heures',
  `prix_vente` decimal(10,2) NOT NULL,
  `date_creation` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_activation` datetime DEFAULT NULL,
  `date_expiration` datetime DEFAULT NULL,
  `hotspot_id` int(11) NOT NULL,
  `client_id` int(11) DEFAULT NULL,
  `vendu_par` int(11) NOT NULL,
  `statut` enum('disponible','vendu','active','expire') NOT NULL DEFAULT 'disponible',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  FOREIGN KEY (`hotspot_id`) REFERENCES `mikrotik_hotspots` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`vendu_par`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des ventes de tickets
CREATE TABLE IF NOT EXISTS `ventes_tickets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `moulin_id` int(11) NOT NULL,
  `prix_vente` decimal(10,2) NOT NULL,
  `commission_vendeur` decimal(10,2) DEFAULT NULL,
  `methode_paiement` enum('especes','mobile_money','carte') NOT NULL DEFAULT 'especes',
  `reference_paiement` varchar(50) DEFAULT NULL,
  `date_vente` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`ticket_id`) REFERENCES `tickets_internet` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des notifications
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_ids` json DEFAULT NULL COMMENT 'Array of user IDs or \"*\" for all',
  `type` varchar(50) DEFAULT 'info',
  `message` text NOT NULL,
  `statut` enum('lue','non_lue') NOT NULL DEFAULT 'non_lue',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des notifications clients
CREATE TABLE IF NOT EXISTS `notifications_clients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `ticket_id` int(11) NOT NULL,
  `type` enum('bienvenue','rappel_expiration','expiration') NOT NULL,
  `message` text NOT NULL,
  `canal` enum('sms','email','systeme') NOT NULL DEFAULT 'systeme',
  `statut` enum('en_attente','envoye','echec') NOT NULL DEFAULT 'en_attente',
  `date_creation` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_envoi` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`ticket_id`) REFERENCES `tickets_internet` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des forfaits
CREATE TABLE IF NOT EXISTS `forfaits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `duree` int(11) NOT NULL COMMENT 'Durée en heures',
  `prix` decimal(10,2) NOT NULL,
  `limite_vitesse` varchar(20) DEFAULT NULL COMMENT 'Format: download/upload (ex: 2M/1M)',
  `limite_donnees` int(11) DEFAULT NULL COMMENT 'Limite en Mo',
  `actif` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nom` (`nom`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertion d'un hotspot Mikrotik de test
INSERT IGNORE INTO `mikrotik_hotspots` (`moulin_id`, `nom`, `adresse_ip`, `port`, `username`, `password`, `ssl`, `actif`)
SELECT m.id, CONCAT(m.nom, ' Hotspot'), '************', 8728, 'admin', 'password', 0, 1
FROM `moulins` m WHERE m.code = 'M001';

-- Insertion de quelques forfaits par défaut
INSERT IGNORE INTO `forfaits` (`nom`, `description`, `duree`, `prix`, `limite_vitesse`, `limite_donnees`, `actif`) VALUES
('Journalier', 'Accès Internet pour 24h', 24, 500.00, '2M/1M', 1024, 1),
('Hebdomadaire', 'Accès Internet pour 7 jours', 168, 2500.00, '3M/1M', 5120, 1),
('Mensuel', 'Accès Internet pour 30 jours', 720, 8000.00, '5M/2M', 20480, 1);


-- ======================================================================
-- Fichier: 05_charges_recettes_setup.sql
-- ======================================================================

-- Module Charges & Recettes
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des catégories de charges
CREATE TABLE IF NOT EXISTS `categorie_charges` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nom` (`nom`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des charges
CREATE TABLE IF NOT EXISTS `charges` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `moulin_id` int(11) NOT NULL,
  `categorie_id` int(11) NOT NULL,
  `description` varchar(255) NOT NULL,
  `montant` decimal(10,2) NOT NULL,
  `date_charge` date NOT NULL,
  `periodicite` enum('ponctuelle','mensuelle','trimestrielle','annuelle') NOT NULL DEFAULT 'ponctuelle',
  `reference_facture` varchar(100) DEFAULT NULL,
  `pieces_justificatives` varchar(255) DEFAULT NULL COMMENT 'Chemin vers les fichiers justificatifs',
  `enregistre_par` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`categorie_id`) REFERENCES `categorie_charges` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`enregistre_par`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des catégories de recettes
CREATE TABLE IF NOT EXISTS `categorie_recettes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nom` (`nom`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des recettes
CREATE TABLE IF NOT EXISTS `recettes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `moulin_id` int(11) NOT NULL,
  `categorie_id` int(11) NOT NULL,
  `description` varchar(255) NOT NULL,
  `montant` decimal(10,2) NOT NULL,
  `date_recette` date NOT NULL,
  `reference` varchar(100) DEFAULT NULL,
  `enregistre_par` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`categorie_id`) REFERENCES `categorie_recettes` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`enregistre_par`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des bilans financiers
CREATE TABLE IF NOT EXISTS `bilans_financiers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `moulin_id` int(11) NOT NULL,
  `periode` varchar(7) NOT NULL COMMENT 'Format: YYYY-MM',
  `total_recettes` decimal(12,2) NOT NULL DEFAULT 0,
  `total_charges` decimal(12,2) NOT NULL DEFAULT 0,
  `benefice_net` decimal(12,2) NOT NULL DEFAULT 0,
  `taux_rentabilite` decimal(5,2) DEFAULT NULL COMMENT 'En pourcentage',
  `genere_par` int(11) NOT NULL,
  `statut` enum('provisoire','finalise') NOT NULL DEFAULT 'provisoire',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `moulin_periode` (`moulin_id`, `periode`),
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`genere_par`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertion des catégories de charges par défaut
INSERT IGNORE INTO `categorie_charges` (`nom`, `description`) VALUES
('Loyer', 'Charges liées au loyer du local'),
('Électricité', 'Factures d''électricité'),
('Salaires', 'Salaires du personnel'),
('Maintenance', 'Entretien et réparation des équipements'),
('Fournitures', 'Achats de fournitures diverses'),
('Taxes', 'Taxes et impôts'),
('Internet', 'Frais de connexion Internet'),
('Divers', 'Autres dépenses non catégorisées');

-- Insertion des catégories de recettes par défaut
INSERT IGNORE INTO `categorie_recettes` (`nom`, `description`) VALUES
('Vente Tickets WiFi', 'Recettes issues de la vente de tickets WiFi'),
('Services Mouture', 'Recettes des services de mouture'),
('Transferts', 'Commissions sur opérations de transfert'),
('Autres Services', 'Recettes provenant d''autres services');


-- ======================================================================
-- Fichier: 06_alertes_setup.sql
-- ======================================================================

-- Module Système d'Alertes Dynamiques
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des types d'alertes
CREATE TABLE IF NOT EXISTS `types_alertes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL,
  `nom` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `niveau` enum('info','attention','urgent','critique') NOT NULL DEFAULT 'info',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des alertes
CREATE TABLE IF NOT EXISTS `alertes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type_id` int(11) NOT NULL,
  `moulin_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `message` text NOT NULL,
  `details` text DEFAULT NULL,
  `date_declenchement` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_resolution` datetime DEFAULT NULL,
  `statut` enum('active','en_traitement','resolue','ignoree') NOT NULL DEFAULT 'active',
  `traite_par` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`type_id`) REFERENCES `types_alertes` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`traite_par`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des conditions de déclenchement
CREATE TABLE IF NOT EXISTS `conditions_alertes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type_id` int(11) NOT NULL,
  `parametrage` json NOT NULL COMMENT 'Configuration JSON des conditions de déclenchement',
  `actif` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`type_id`) REFERENCES `types_alertes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des notifications d'alertes
CREATE TABLE IF NOT EXISTS `notifications_alertes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `alerte_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `canal` enum('email','sms','systeme') NOT NULL DEFAULT 'systeme',
  `statut` enum('en_attente','envoyee','echec','lue') NOT NULL DEFAULT 'en_attente',
  `date_envoi` datetime DEFAULT NULL,
  `date_lecture` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`alerte_id`) REFERENCES `alertes` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertion des types d'alertes par défaut
INSERT IGNORE INTO `types_alertes` (`code`, `nom`, `description`, `niveau`) VALUES
('STOCK_BAS', 'Stock de tickets bas', 'Alerte lorsque le stock de tickets est faible', 'attention'),
('PANNE_MOULIN', 'Panne de moulin', 'Alerte lors d''une panne d''un moulin', 'urgent'),
('INACTIVITE', 'Inactivité détectée', 'Alerte lorsqu''un moulin n''a pas eu d''activité depuis longtemps', 'attention'),
('PERFORMANCE_BASSE', 'Performance faible', 'Alerte lorsque les performances sont inférieures aux objectifs', 'info'),
('MAINTENANCE_REQUISE', 'Maintenance requise', 'Alerte pour maintenance préventive programmée', 'attention'),
('TENTATIVE_ACCES', 'Tentatives d''accès non autorisées', 'Alerte de sécurité pour tentatives d''accès non autorisées', 'critique');

-- Insertion de quelques conditions d'alertes par défaut
INSERT IGNORE INTO `conditions_alertes` (`type_id`, `parametrage`, `actif`)
SELECT ta.id, '{"seuil": 10, "verification_intervalle": "daily"}', 1
FROM `types_alertes` ta WHERE ta.code = 'STOCK_BAS';

INSERT IGNORE INTO `conditions_alertes` (`type_id`, `parametrage`, `actif`)
SELECT ta.id, '{"jours_inactivite": 3, "verification_intervalle": "daily"}', 1
FROM `types_alertes` ta WHERE ta.code = 'INACTIVITE';

INSERT IGNORE INTO `conditions_alertes` (`type_id`, `parametrage`, `actif`)
SELECT ta.id, '{"seuil_performance": 70, "periode": "monthly"}', 1
FROM `types_alertes` ta WHERE ta.code = 'PERFORMANCE_BASSE';


-- ======================================================================
-- Fichier: 07_rapports_statistiques_setup.sql
-- ======================================================================

-- Module Rapports & Statistiques
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des rapports
CREATE TABLE IF NOT EXISTS `rapports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `titre` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `type` enum('ventes','financier','performances','activite','personnalise') NOT NULL,
  `parametres` json DEFAULT NULL COMMENT 'Configuration JSON du rapport',
  `periode_debut` date DEFAULT NULL,
  `periode_fin` date DEFAULT NULL,
  `createur_id` int(11) NOT NULL,
  `moulin_id` int(11) DEFAULT NULL COMMENT 'NULL si rapport global',
  `format` enum('html','csv','pdf','json') NOT NULL DEFAULT 'html',
  `fichier_genere` varchar(255) DEFAULT NULL COMMENT 'Chemin vers le fichier généré',
  `date_generation` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `cron_expression` varchar(100) DEFAULT NULL COMMENT 'Expression cron pour génération automatique',
  PRIMARY KEY (`id`),
  UNIQUE KEY `titre_unique` (`titre`),
  FOREIGN KEY (`createur_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des statistiques agrégées (pour mise en cache et amélioration des performances)
CREATE TABLE IF NOT EXISTS `statistiques_agregees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cle` varchar(100) NOT NULL,
  `type` varchar(50) NOT NULL,
  `periode` varchar(10) NOT NULL COMMENT 'Format: YYYY-MM ou YYYY-MM-DD',
  `moulin_id` int(11) DEFAULT NULL,
  `gerant_id` VARCHAR(10) DEFAULT NULL,
  `valeur` decimal(12,2) NOT NULL,
  `metadonnees` json DEFAULT NULL COMMENT 'Données supplémentaires au format JSON',
  `date_calcul` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `stats_unique` (`cle`, `type`, `periode`, `moulin_id`, `gerant_id`),
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`gerant_id`) REFERENCES `gerants` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des vues de tableaux de bord sauvegardées
CREATE TABLE IF NOT EXISTS `dashboard_views` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `nom` varchar(100) NOT NULL,
  `configuration` json NOT NULL COMMENT 'Configuration JSON du tableau de bord',
  `par_defaut` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_view_name` (`user_id`, `nom`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Création des vues SQL pour faciliter la génération de rapports

-- Vue pour les ventes de tickets par jour et par moulin
CREATE OR REPLACE VIEW `vue_ventes_tickets_par_jour` AS
SELECT 
  v.moulin_id,
  m.nom AS nom_moulin,
  DATE(v.date_vente) AS date_vente,
  COUNT(v.id) AS nombre_tickets,
  SUM(v.prix_vente) AS montant_total,
  SUM(v.commission_vendeur) AS commission_totale
FROM ventes_tickets v
JOIN moulins m ON v.moulin_id = m.id
GROUP BY v.moulin_id, DATE(v.date_vente)
ORDER BY DATE(v.date_vente) DESC;

-- Vue pour les performances des gérants
CREATE OR REPLACE VIEW `vue_performances_gerants` AS
SELECT 
  p.gerant_id,
  g.nom AS nom_gerant,
  p.moulin_id,
  m.nom AS nom_moulin,
  p.periode,
  p.ventes_total,
  p.objectif_ventes,
  p.taux_realisation,
  p.evaluation
FROM performances p
JOIN gerants g ON p.gerant_id = g.id
JOIN moulins m ON p.moulin_id = m.id
ORDER BY p.periode DESC, p.taux_realisation DESC;

-- Vue pour le bilan financier mensuel par moulin
CREATE OR REPLACE VIEW `vue_bilan_financier_mensuel` AS
SELECT 
  m.id AS moulin_id,
  m.nom AS nom_moulin,
  DATE_FORMAT(r.date_recette, '%Y-%m') AS periode,
  SUM(r.montant) AS total_recettes,
  (
    SELECT COALESCE(SUM(c.montant), 0)
    FROM charges c
    WHERE c.moulin_id = m.id AND DATE_FORMAT(c.date_charge, '%Y-%m') = DATE_FORMAT(r.date_recette, '%Y-%m')
  ) AS total_charges,
  SUM(r.montant) - (
    SELECT COALESCE(SUM(c.montant), 0)
    FROM charges c
    WHERE c.moulin_id = m.id AND DATE_FORMAT(c.date_charge, '%Y-%m') = DATE_FORMAT(r.date_recette, '%Y-%m')
  ) AS benefice_net,
  CASE 
    WHEN (
      SELECT COALESCE(SUM(c.montant), 0)
      FROM charges c
      WHERE c.moulin_id = m.id AND DATE_FORMAT(c.date_charge, '%Y-%m') = DATE_FORMAT(r.date_recette, '%Y-%m')
    ) > 0 THEN
      (SUM(r.montant) - (
        SELECT COALESCE(SUM(c.montant), 0)
        FROM charges c
        WHERE c.moulin_id = m.id AND DATE_FORMAT(c.date_charge, '%Y-%m') = DATE_FORMAT(r.date_recette, '%Y-%m')
      )) / (
        SELECT COALESCE(SUM(c.montant), 0)
        FROM charges c
        WHERE c.moulin_id = m.id AND DATE_FORMAT(c.date_charge, '%Y-%m') = DATE_FORMAT(r.date_recette, '%Y-%m')
      ) * 100
    ELSE 0
  END AS taux_rentabilite
FROM moulins m
JOIN recettes r ON m.id = r.moulin_id
GROUP BY m.id, DATE_FORMAT(r.date_recette, '%Y-%m')
ORDER BY DATE_FORMAT(r.date_recette, '%Y-%m') DESC;

-- Vue pour l'activité des hotspots par moulin
CREATE OR REPLACE VIEW `vue_activite_hotspots` AS
SELECT 
  m.id AS moulin_id,
  m.nom AS nom_moulin,
  h.id AS hotspot_id,
  h.nom AS nom_hotspot,
  DATE(t.date_activation) AS date,
  COUNT(t.id) AS nombre_activations,
  COUNT(DISTINCT t.client_id) AS nombre_clients_uniques
FROM moulins m
JOIN mikrotik_hotspots h ON m.id = h.moulin_id
JOIN tickets_internet t ON h.id = t.hotspot_id
WHERE t.date_activation IS NOT NULL
GROUP BY m.id, h.id, DATE(t.date_activation)
ORDER BY DATE(t.date_activation) DESC;

-- Création d'un rapport de test
INSERT IGNORE INTO `rapports` (`titre`, `description`, `type`, `parametres`, `periode_debut`, `periode_fin`, `createur_id`, `format`)
SELECT 
  'Rapport de ventes mensuel', 
  'Rapport détaillant les ventes de tickets par moulin pour le mois en cours', 
  'ventes',
  '{"groupBy": "moulin", "includeCharts": true}',
  DATE_FORMAT(NOW(), '%Y-%m-01'),
  LAST_DAY(NOW()),
  u.id,
  'html'
FROM users u 
WHERE u.username = 'admin'
LIMIT 1;


-- ======================================================================
-- Fichier: 08_transferts_fonds_setup.sql
-- ======================================================================

-- Module de Transfert de Fonds
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des services de transfert
CREATE TABLE IF NOT EXISTS `services_transfert` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(20) NOT NULL,
  `nom` varchar(100) NOT NULL,
  `type` enum('national','international') NOT NULL DEFAULT 'national',
  `description` text DEFAULT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `commission_fixe` decimal(10,2) DEFAULT NULL,
  `commission_pourcentage` decimal(5,2) DEFAULT NULL,
  `devise_principale` varchar(3) NOT NULL DEFAULT 'XOF',
  `devises_supportees` json DEFAULT NULL,
  `actif` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des taux de change
CREATE TABLE IF NOT EXISTS `taux_change` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `devise_source` varchar(3) NOT NULL,
  `devise_cible` varchar(3) NOT NULL,
  `taux` decimal(15,6) NOT NULL,
  `date_effet` date NOT NULL,
  `source` varchar(100) DEFAULT NULL COMMENT 'Source du taux de change',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `taux_unique` (`devise_source`, `devise_cible`, `date_effet`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des transferts
CREATE TABLE IF NOT EXISTS `transferts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reference` varchar(20) NOT NULL,
  `service_id` int(11) NOT NULL,
  `moulin_id` int(11) NOT NULL,
  `agent_id` int(11) NOT NULL,
  `type` enum('envoi','reception') NOT NULL,
  `montant` decimal(15,2) NOT NULL,
  `devise` varchar(3) NOT NULL DEFAULT 'XOF',
  `commission` decimal(10,2) NOT NULL DEFAULT 0,
  `commission_agent` decimal(10,2) DEFAULT 0,
  `statut` enum('en_attente','valide','complete','annule','echoue') NOT NULL DEFAULT 'en_attente',
  `date_operation` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_validation` datetime DEFAULT NULL,
  `valide_par` int(11) DEFAULT NULL,
  `motif_annulation` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `reference` (`reference`),
  FOREIGN KEY (`service_id`) REFERENCES `services_transfert` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`agent_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`valide_par`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des détails des expéditeurs et bénéficiaires
CREATE TABLE IF NOT EXISTS `details_transfert` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transfert_id` int(11) NOT NULL,
  `expediteur_nom` varchar(100) NOT NULL,
  `expediteur_prenom` varchar(100) NOT NULL,
  `expediteur_telephone` varchar(20) NOT NULL,
  `expediteur_piece_identite` varchar(50) DEFAULT NULL,
  `expediteur_adresse` varchar(255) DEFAULT NULL,
  `beneficiaire_nom` varchar(100) NOT NULL,
  `beneficiaire_prenom` varchar(100) NOT NULL,
  `beneficiaire_telephone` varchar(20) NOT NULL,
  `beneficiaire_adresse` varchar(255) DEFAULT NULL,
  `question_secrete` varchar(255) DEFAULT NULL,
  `reponse_secrete` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`transfert_id`) REFERENCES `transferts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table de l'historique des statuts des transferts
CREATE TABLE IF NOT EXISTS `historique_statut_transfert` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transfert_id` int(11) NOT NULL,
  `statut_precedent` varchar(20) DEFAULT NULL,
  `statut_nouveau` varchar(20) NOT NULL,
  `user_id` int(11) NOT NULL,
  `commentaire` text DEFAULT NULL,
  `date_modification` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`transfert_id`) REFERENCES `transferts` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertion des services de transfert par défaut
INSERT IGNORE INTO `services_transfert` (`code`, `nom`, `type`, `description`, `commission_fixe`, `commission_pourcentage`, `devise_principale`, `devises_supportees`, `actif`) VALUES
('MIXX', 'Mixx by Yass', 'national', 'Service de transfert mobile national', 500.00, 1.50, 'XOF', '["XOF"]', 1),
('FLOOZ', 'Flooz', 'national', 'Service de transfert mobile par Moov Africa', 300.00, 1.00, 'XOF', '["XOF"]', 1),
('TMONEY', 'T-Money', 'national', 'Service de transfert mobile par Togocom', 300.00, 1.00, 'XOF', '["XOF"]', 1),
('WARI', 'Wari', 'international', 'Service de transfert d''argent international', 1000.00, 2.50, 'XOF', '["XOF", "EUR", "USD", "GHS", "NGN"]', 0),
('WESTERN', 'Western Union', 'international', 'Service de transfert d''argent international', 1500.00, 3.50, 'XOF', '["XOF", "EUR", "USD", "GBP", "CAD"]', 0);

-- Insertion de quelques taux de change par défaut
INSERT IGNORE INTO `taux_change` (`devise_source`, `devise_cible`, `taux`, `date_effet`, `source`) VALUES
('EUR', 'XOF', 655.957, CURDATE(), 'Banque Centrale'),
('USD', 'XOF', 600.000, CURDATE(), 'Taux moyen'),
('GBP', 'XOF', 770.000, CURDATE(), 'Taux moyen'),
('GHS', 'XOF', 50.000, CURDATE(), 'Taux moyen'),
('NGN', 'XOF', 0.750, CURDATE(), 'Taux moyen');
