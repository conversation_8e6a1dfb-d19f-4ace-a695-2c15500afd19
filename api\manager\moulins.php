<?php
// api/manager/moulins.php

// En-tête CORS géré par le fichier cors.php principal
require_once __DIR__ . '/../cors.php';
header('Content-Type: application/json');

require_once __DIR__ . '/../config.php';

function getMoulinsWithGerants() {
    global $pdo;

    try {
        $sql = "SELECT m.id, m.nom, m.adresse, m.statut, m.puissance, m.created_at, u.nom as gerant_nom, u.id as gerant_id
                FROM moulins m
                LEFT JOIN users u ON m.gerant_id = u.id
                ORDER BY m.created_at DESC";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $moulins = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode(['data' => $moulins]);

    } catch (PDOException $e) {
        http_response_code(500);
        // Journaliser l'erreur réelle pour le débogage
        error_log('<PERSON> Moulins (Manager) Error: ' . $e->getMessage());
        // Envoyer une réponse générique
        echo json_encode(['message' => 'Une erreur est survenue lors de la récupération des données des moulins.']);
    }
}

getMoulinsWithGerants();
