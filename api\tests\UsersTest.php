<?php

use PHPUnit\Framework\TestCase;

class UsersTest extends TestCase
{
    private $pdo;
    
    protected function setUp(): void
    {
        // Initialiser la connexion à la base de données de test
        $this->pdo = getTestDbConnection();
    }
    
    public function testUserAuthentication()
    {
        // Simuler la fonction d'authentification du fichier login.php
        $username = 'testuser';
        $password = 'password123';
        
        $stmt = $this->pdo->prepare("SELECT * FROM users WHERE username = ? AND active = 1");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Vérifier que l'utilisateur existe
        $this->assertNotFalse($user);
        
        // Vérifier la validation du mot de passe
        $this->assertTrue(password_verify($password, $user['password']));
    }
    
    public function testUserRolesAndPermissions()
    {
        // Test pour vérifier que les rôles sont correctement attribués
        $stmt = $this->pdo->prepare("
            SELECT r.name FROM users u
            JOIN roles r ON u.role = r.name
            WHERE u.username = ?
        ");
        $stmt->execute(['testuser']);
        $role = $stmt->fetchColumn();
        
        $this->assertEquals('admin', $role);
        
        // Test pour vérifier que les permissions sont correctement assignées
        $stmt = $this->pdo->prepare("
            SELECT COUNT(p.id) FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            JOIN roles r ON rp.role_id = r.id
            WHERE r.name = 'admin'
        ");
        $stmt->execute();
        $permissionCount = $stmt->fetchColumn();
        
        $this->assertGreaterThan(0, $permissionCount);
    }
    
    public function testUserCreation()
    {
        // Tester la création d'un nouvel utilisateur
        $newUsername = 'newuser_' . time();
        $newEmail = 'new_' . time() . '@example.com';
        $hashedPassword = password_hash('newpassword123', PASSWORD_DEFAULT);
        
        $stmt = $this->pdo->prepare("
            INSERT INTO users (username, password, email, role, active)
            VALUES (?, ?, ?, ?, ?)
        ");
        $result = $stmt->execute([$newUsername, $hashedPassword, $newEmail, 'vendeur', 1]);
        
        $this->assertTrue($result);
        
        // Vérifier que l'utilisateur a bien été créé
        $stmt = $this->pdo->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute([$newUsername]);
        $newUser = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertNotFalse($newUser);
        $this->assertEquals($newEmail, $newUser['email']);
        $this->assertEquals('vendeur', $newUser['role']);
    }
    
    protected function tearDown(): void
    {
        // Nettoyer les données de test si nécessaire
        $this->pdo = null;
    }
}
