<?php
// Fichier: api/fonctions/auth_check.php

function get_connected_user() {
    // --- SIMULATION --- 
    // Remplacer cette partie par votre vrai système de session plus tard
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    // Pour les tests, on peut forcer un utilisateur si la session est vide
    if (!isset($_SESSION['user'])) {
        $_SESSION['user'] = [
            'id' => 1, // Simule l'ID de l'utilisateur connecté
            'role' => 'admin' // Simule le rôle: 'admin' ou 'gerant'
        ];
    }
    return $_SESSION['user'];
    // --- FIN SIMULATION ---
}

function check_auth_and_moulin_access($pdo, $moulin_id) {
    $user = get_connected_user();

    if (!$user) {
        http_response_code(401); // Non autorisé
        echo json_encode(['success' => false, 'message' => 'Accès non autorisé. Utilisateur non connecté.']);
        exit;
    }

    // L'admin a accès à tout
    if ($user['role'] === 'admin') {
        return $user;
    }

    // Le gérant ne peut voir que les infos de son moulin
    if ($user['role'] === 'gerant') {
        $stmt = $pdo->prepare("SELECT gerant_id FROM moulins WHERE id = ?");
        $stmt->execute([$moulin_id]);
        $moulin = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$moulin || $moulin['gerant_id'] != $user['id']) {
            http_response_code(403); // Interdit
            echo json_encode(['success' => false, 'message' => 'Accès interdit. Vous n\'êtes pas le gérant de ce moulin.']);
            exit;
        }
    }
    
    return $user;
}
?>
