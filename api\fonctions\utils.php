<?php
/**
 * Fonctions utilitaires pour l'API
 * Ce fichier contient des fonctions génériques utilisées dans toute l'API
 */

/**
 * Génère une réponse JSON d'erreur et termine le script
 * @param string $message Message d'erreur
 * @param int $code Code HTTP (défaut: 400 Bad Request)
 * @return void
 */
function jsonError($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message
    ]);
    exit();
}

/**
 * Génère une réponse JSON de succès
 * @param array $data Données à renvoyer
 * @param string $message Message de succès (optionnel)
 * @param int $code Code HTTP (défaut: 200 OK)
 * @return void
 */
function jsonSuccess($data = [], $message = null, $code = 200) {
    http_response_code($code);
    $response = ['success' => true];
    
    if ($message !== null) {
        $response['message'] = $message;
    }
    
    if (!empty($data)) {
        $response['data'] = $data;
    }
    
    echo json_encode($response);
    exit();
}

/**
 * Valide une adresse email
 * @param string $email L'adresse email à valider
 * @return bool True si l'email est valide, false sinon
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Génère un mot de passe aléatoire
 * @param int $length Longueur du mot de passe (défaut: 10)
 * @return string Mot de passe généré
 */
function generateRandomPassword($length = 10) {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';
    $count = strlen($chars);
    $password = '';
    
    for ($i = 0; $i < $length; $i++) {
        $password .= $chars[random_int(0, $count - 1)];
    }
    
    return $password;
}

/**
 * Vérifie si une table existe dans la base de données
 * @param PDO $conn Connexion à la base de données
 * @param string $tableName Nom de la table à vérifier
 * @return bool True si la table existe, false sinon
 */
function tableExists($conn, $tableName) {
    try {
        $result = $conn->query("SELECT 1 FROM {$tableName} LIMIT 1");
        return $result !== false;
    } catch (Exception $e) {
        return false;
    }
}
