/* Styles personnalisés pour la gestion des utilisateurs */

/* Animations d'entrée */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Classes d'animation */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.animate-pulse-gentle {
  animation: pulse 2s infinite;
}

/* Classes d'animation avec délais */
.stat-card:nth-child(1) {
  animation-delay: 0.1s;
}

.stat-card:nth-child(2) {
  animation-delay: 0.2s;
}

.stat-card:nth-child(3) {
  animation-delay: 0.3s;
}

.stat-card:nth-child(4) {
  animation-delay: 0.4s;
}

.user-card:nth-child(1) {
  animation-delay: 0.6s;
}

.user-card:nth-child(2) {
  animation-delay: 0.7s;
}

.user-card:nth-child(3) {
  animation-delay: 0.8s;
}

.user-card:nth-child(4) {
  animation-delay: 0.9s;
}

.user-card:nth-child(5) {
  animation-delay: 1.0s;
}

/* Effets de survol pour les cartes utilisateur */
.user-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.user-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.user-card:hover::before {
  left: 100%;
}

.user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Styles pour les badges de rôle avec gradients */
.role-badge-admin {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(220, 38, 38, 0.3);
}

.role-badge-supervisor {
  background: linear-gradient(135deg, #7c3aed, #8b5cf6);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(124, 58, 237, 0.3);
}

.role-badge-operator {
  background: linear-gradient(135deg, #059669, #10b981);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(5, 150, 105, 0.3);
}

/* Styles pour les badges de statut */
.status-badge-active {
  background: linear-gradient(135deg, #059669, #10b981);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(5, 150, 105, 0.3);
}

.status-badge-inactive {
  background: linear-gradient(135deg, #6b7280, #9ca3af);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
}

.status-badge-suspended {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(220, 38, 38, 0.3);
}

/* Effets de survol pour les boutons d'action */
.action-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.action-button:hover::before {
  width: 100px;
  height: 100px;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Styles pour les statistiques */
.stat-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.stat-card:hover::before {
  transform: translateX(100%);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Styles pour les avatars avec effet de halo */
.avatar-glow {
  position: relative;
}

.avatar-glow::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s;
}

.avatar-glow:hover::after {
  opacity: 1;
}

/* Styles pour les filtres */
.filter-section {
  background: linear-gradient(135deg, rgba(249, 250, 251, 0.8), rgba(239, 246, 255, 0.8));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(229, 231, 235, 0.8);
}

/* Responsive design amélioré */
@media (max-width: 768px) {
  .user-card {
    padding: 1rem;
  }
  
  .stat-card {
    padding: 0.75rem;
  }
  
  .action-button {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
}

/* Styles pour les états de chargement */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Styles pour les transitions de page */
.page-transition {
  animation: fadeInUp 0.8s ease-out;
}

/* Styles pour les tooltips personnalisés */
.custom-tooltip {
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
}

/* Styles pour les indicateurs de statut en temps réel */
.status-indicator {
  position: relative;
}

.status-indicator.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: rgba(34, 197, 94, 0.3);
  animation: pulse 2s infinite;
}

/* Styles pour les notifications */
.notification-badge {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-radius: 9999px;
  padding: 0.125rem 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.3);
  animation: pulse 2s infinite;
}
