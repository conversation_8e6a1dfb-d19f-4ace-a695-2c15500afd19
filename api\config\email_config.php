<?php
/**
 * Configuration pour l'envoi d'emails
 * 
 * Ce fichier contient les paramètres SMTP pour l'envoi d'emails
 * Modifiez ces paramètres selon votre fournisseur de messagerie
 */

// Configuration SMTP
return [
    // Activer l'envoi d'email (false pour désactiver en développement)
    'enabled' => true,

    // Méthode d'envoi : 'smtp' ou 'mail' (fonction mail() native PHP)
    // En développement, utiliser 'mail' ou 'log' pour simuler
    // En production, utiliser 'mail' (si serveur configuré) ou 'smtp'
    'method' => 'mail', // 'smtp', 'mail', ou 'log' (simulation)
    
    // Configuration SMTP
    'smtp' => [
        'host' => 'smtp.gmail.com',        // Serveur SMTP (Gmail, Outlook, etc.)
        'port' => 587,                     // Port SMTP (587 pour TLS, 465 pour SSL)
        'encryption' => 'tls',             // 'tls', 'ssl' ou false
        'username' => '<EMAIL>',  // Votre adresse email
        'password' => 'votre-mot-de-passe-app',  // Mot de passe d'application Gmail
        'timeout' => 30,                   // Timeout en secondes
    ],
    
    // Paramètres par défaut de l'expéditeur
    'from' => [
        'email' => '<EMAIL>',
        'name' => 'Service WiFi Moulin'
    ],
    
    // Configuration pour différents fournisseurs
    'providers' => [
        'gmail' => [
            'host' => 'smtp.gmail.com',
            'port' => 587,
            'encryption' => 'tls'
        ],
        'outlook' => [
            'host' => 'smtp-mail.outlook.com',
            'port' => 587,
            'encryption' => 'tls'
        ],
        'yahoo' => [
            'host' => 'smtp.mail.yahoo.com',
            'port' => 587,
            'encryption' => 'tls'
        ]
    ]
];
