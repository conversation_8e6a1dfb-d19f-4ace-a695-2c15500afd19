<?php
/**
 * mark-read.php
 * 
 * API endpoint pour marquer une notification de ticket comme lue
 */

// Configuration pour assurer que les erreurs PHP sont retournées en JSON
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Utiliser le système CORS centralisé
require_once __DIR__ . '/../cors.php';

// Inclusion de la fonction de log
require_once '../utils/logger.php';

// Ajouter Content-Type uniquement pour les réponses non-OPTIONS
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    // Commenté pour éviter duplication: // Commenté pour éviter duplication: header("Content-Type: application/json; charset=UTF-8");
}


// Vérifier si c'est une requête POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Méthode non autorisée. Utilisez POST.'
    ]);
    exit;
}

// Récupération des données
$jsonData = file_get_contents("php://input");
$data = json_decode($jsonData);

// Validation des données
if (!$data || !isset($data->notification_id)) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error', 
        'message' => 'Données incomplètes: notification_id requis'
    ]);
    logMessage("Données incomplètes pour marquer la notification comme lue", [
        'reçu' => json_decode($jsonData, true),
        'attendu' => 'notification_id'
    ], 'error');
    exit;
}

try {
    // Connexion à la base de données
    require_once '../config.php';
    
    // Vérifier que la notification existe
    $stmt = $pdo->prepare("SELECT * FROM notifications WHERE id = ?");
    $stmt->execute([$data->notification_id]);
    $notification = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$notification) {
        throw new Exception("Notification non trouvée");
    }
    
    // Mettre à jour le statut de la notification comme "read"
    $stmt = $pdo->prepare("
        UPDATE notifications 
        SET status = 'read',
            read_at = NOW(),
            updated_at = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$data->notification_id]);
    
    if ($stmt->rowCount() > 0) {
        // Réponse de succès
        echo json_encode([
            'status' => 'success',
            'message' => 'Notification marquée comme lue'
        ]);
        
        logMessage("Notification marquée comme lue", [
            'notification_id' => $data->notification_id
        ]);
    } else {
        // Si aucune ligne n'a été mise à jour, c'est probablement déjà lu
        echo json_encode([
            'status' => 'success',
            'message' => 'Notification déjà lue ou non modifiable'
        ]);
    }
    
} catch (Exception $e) {
    // Journaliser l'erreur
    logMessage("Erreur lors du marquage de la notification comme lue", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'notification_id' => isset($data->notification_id) ? $data->notification_id : 'unknown'
    ], 'error');
    
    // Réponse d'erreur
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Erreur lors du marquage comme lu: ' . $e->getMessage()
    ]);
}
?>
