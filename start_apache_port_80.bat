@echo off
echo ========================================
echo Demarrage Apache sur port 80
echo ========================================

echo 1. Arret des processus Apache existants...
taskkill /F /IM httpd.exe 2>nul
timeout /t 2 >nul

echo 2. Modification de la configuration Apache...
cd /d C:\xampp\apache\conf
if exist httpd.conf.backup (
    echo Restauration de la configuration originale...
    copy httpd.conf.backup httpd.conf /Y
) else (
    echo Sauvegarde de la configuration actuelle...
    copy httpd.conf httpd.conf.backup /Y
)

echo 3. Configuration du port 80...
powershell -Command "(Get-Content httpd.conf) -replace 'Listen 8080', 'Listen 80' | Set-Content httpd.conf"

echo 4. Demarrage d'Apache...
cd /d C:\xampp
apache\bin\httpd.exe -D FOREGROUND

pause
