-- Module <PERSON>nts
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des gérants (structure alignée avec gerants.php)
CREATE TABLE IF NOT EXISTS `gerants` (
  `id` VARCHAR(10) NOT NULL PRIMARY KEY,
  `user_id` INT(11) NOT NULL,
  `nom` VARCHAR(100) NOT NULL,
  `telephone` VARCHAR(20) NOT NULL,
  `email` VARCHAR(100) NOT NULL,
  `statut` VARCHAR(50) DEFAULT 'Actif',
  `dateEmbauche` DATE,
  `moulinsAssignes` JSO<PERSON> DEFAULT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des absences de gérants
CREATE TABLE IF NOT EXISTS `absences` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `gerant_id` VARCHAR(10) NOT NULL,
  `type` enum('conge','maladie','absence_justifiee','absence_non_justifiee') NOT NULL,
  `date_debut` date NOT NULL,
  `date_fin` date NOT NULL,
  `justification` text DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`gerant_id`) REFERENCES `gerants` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des performances des gérants
CREATE TABLE IF NOT EXISTS `performances` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `gerant_id` VARCHAR(10) NOT NULL,
  `moulin_id` INT(11) NOT NULL,
  `periode` varchar(7) NOT NULL COMMENT 'Format: YYYY-MM',
  `ventes_total` decimal(12,2) DEFAULT 0,
  `objectif_ventes` decimal(12,2) DEFAULT 0,
  `taux_realisation` decimal(5,2) DEFAULT 0,
  `evaluation` enum('excellent','bon','moyen','insuffisant') DEFAULT NULL,
  `commentaire` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `gerant_periode_moulin` (`gerant_id`, `periode`, `moulin_id`),
  FOREIGN KEY (`gerant_id`) REFERENCES `gerants` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
