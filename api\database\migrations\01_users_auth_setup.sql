-- Module Utilisateurs et Authentification
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des rôles
CREATE TABLE IF NOT EXISTS `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertion des rôles par défaut
INSERT IGNORE INTO `roles` (`name`, `description`) VALUES
('admin', 'Administrateur avec accès complet'),
('superviseur', 'Superviseur avec accès à la gestion globale'),
('gerant', 'Gérant de moulin'),
('vendeur', 'Vendeur de tickets'),
('agent_transfert', 'Agent pour les opérations de transfert');

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `role_id` int(11) NOT NULL,
  `statut` VARCHAR(50) NOT NULL DEFAULT 'Actif',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_login` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Création d'un utilisateur admin par défaut (mot de passe: admin123)
INSERT IGNORE INTO `users` (`username`, `password`, `email`, `role_id`, `statut`, `created_at`)
VALUES ('admin', '$2y$10$vuwu8ZDlXiHgKhkOBYpK8OdPuwB26aRA5jWys.DDCTc4zPGmpUgd', '<EMAIL>', (SELECT id FROM roles WHERE name = 'admin'), 'Actif', NOW());

-- Création d'un utilisateur gérant de test (mot de passe: manager123)
INSERT IGNORE INTO `users` (`username`, `password`, `email`, `role_id`, `statut`, `created_at`)
VALUES ('manager', '$2y$10$ZYtSOBAttu7IOY1RPRtyGepkeGQQQjkSVM.c2Vqk8QJkqNwQmQZp', '<EMAIL>', (SELECT id FROM roles WHERE name = 'gerant'), 'Actif', NOW());

-- Table des permissions
CREATE TABLE IF NOT EXISTS `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertion des permissions par défaut
INSERT IGNORE INTO `permissions` (`name`, `description`) VALUES
('manage_users', 'Gérer les utilisateurs'),
('view_reports', 'Voir les rapports'),
('manage_moulins', 'Gérer les moulins'),
('manage_gerants', 'Gérer les gérants'),
('sell_tickets', 'Vendre des tickets'),
('manage_finances', 'Gérer les finances'),
('manage_alerts', 'Gérer les alertes'),
('manage_transferts', 'Gérer les transferts de fonds');

-- Table de liaison rôles-permissions
CREATE TABLE IF NOT EXISTS `role_permissions` (
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`role_id`, `permission_id`),
  FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Attribution des permissions aux rôles
-- Admin a toutes les permissions
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id FROM `roles` r, `permissions` p WHERE r.name = 'admin';

-- Superviseur a la plupart des permissions sauf la gestion des utilisateurs
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id FROM `roles` r, `permissions` p 
WHERE r.name = 'superviseur' AND p.name != 'manage_users';

-- Gérant peut gérer son moulin, ses finances et voir les rapports
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id FROM `roles` r, `permissions` p 
WHERE r.name = 'gerant' AND p.name IN ('manage_moulins', 'manage_finances', 'view_reports');

-- Vendeur peut vendre des tickets
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id FROM `roles` r, `permissions` p 
WHERE r.name = 'vendeur' AND p.name = 'sell_tickets';

-- Agent de transfert peut gérer les transferts
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id FROM `roles` r, `permissions` p 
WHERE r.name = 'agent_transfert' AND p.name = 'manage_transferts';

-- Table de l'historique de connexion
CREATE TABLE IF NOT EXISTS `login_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `login_time` datetime NOT NULL,
  `logout_time` datetime DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
