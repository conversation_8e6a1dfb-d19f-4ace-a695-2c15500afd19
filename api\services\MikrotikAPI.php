<?php
/**
 * Service d'intégration avec l'API Mikrotik
 * Ce service permet de communiquer directement avec un routeur Mikrotik
 * pour gérer les utilisateurs hotspot, tickets, et autres fonctionnalités.
 * 
 * <AUTHOR>
 * @version 1.0
 */

class MikrotikAPI {
    private $host;
    private $username;
    private $password;
    private $port;
    private $debug;
    private $connection;
    private $attempts = 3; // Nombre de tentatives de connexion

    /**
     * Constructeur
     * 
     * @param string $host Adresse IP ou nom d'hôte du routeur
     * @param string $username Nom d'utilisateur pour la connexion
     * @param string $password Mot de passe
     * @param int $port Port API (8728 par défaut, 8729 pour SSL)
     * @param bool $debug Activer le mode débogage
     */
    public function __construct($host, $username, $password, $port = 8728, $debug = false) {
        $this->host = $host;
        $this->username = $username;
        $this->password = $password;
        $this->port = $port;
        $this->debug = $debug;
        $this->connection = null;
    }

    /**
     * Établit une connexion au routeur Mikrotik
     * 
     * @return bool Succès de la connexion
     */
    public function connect() {
        // Vérifier si la fonction socket_create est disponible
        if (!function_exists('socket_create')) {
            $this->log("Extension PHP Socket non disponible");
            return false;
        }

        // Tentatives de connexion avec retry
        $attempt = 0;
        while ($attempt < $this->attempts) {
            $this->connection = @socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
            
            if ($this->connection) {
                // Réduire le timeout à 3 secondes pour un échec plus rapide
                socket_set_option($this->connection, SOL_SOCKET, SO_RCVTIMEO, ['sec' => 3, 'usec' => 0]);
                socket_set_option($this->connection, SOL_SOCKET, SO_SNDTIMEO, ['sec' => 3, 'usec' => 0]);
                
                $this->log("Tentative de connexion à {$this->host}:{$this->port}");
                
                if (@socket_connect($this->connection, $this->host, $this->port)) {
                    $this->log("Connexion réussie");
                    
                    // Authentification
                    if ($this->login()) {
                        $this->log("Authentification réussie");
                        return true;
                    } else {
                        $this->log("Échec d'authentification");
                        socket_close($this->connection);
                    }
                } else {
                    $this->log("Échec de connexion: " . socket_strerror(socket_last_error($this->connection)));
                    socket_close($this->connection);
                }
            } else {
                $this->log("Impossible de créer le socket: " . socket_strerror(socket_last_error()));
            }
            
            $attempt++;
            if ($attempt < $this->attempts) {
                $this->log("Nouvelle tentative dans 2 secondes...");
                sleep(2);
            }
        }
        
        return false;
    }

    /**
     * S'authentifie sur le routeur
     * 
     * @return bool Succès de l'authentification
     */
    private function login() {
        // Gérer le cas spécial de l'authentification sans mot de passe (ex: demo.mt.lv)
        if (empty($this->password)) {
            $this->log("Tentative de connexion sans mot de passe pour l'utilisateur {$this->username}");
            $this->writeWord("/login");
            $this->writeWord("=name=" . $this->username);
            $this->writeWord("");
        } else {
            // Processus d'authentification standard avec challenge-response
            $this->writeWord("/login");
            $this->writeWord("");
            
            $response = $this->readResponse();
            if (!isset($response[0]['ret'])) {
                $this->log("Challenge d'authentification non reçu");
                return false;
            }
            
            $challenge = $response[0]['ret'];
            $this->log("Challenge reçu: $challenge");
            
            $token = '00' . md5(chr(0) . $this->password . pack('H*', $challenge));
            
            $this->writeWord("/login");
            $this->writeWord("=name=" . $this->username);
            $this->writeWord("=response=" . $token);
            $this->writeWord("");
        }
        
        // Étape 6: Vérifier la réponse
        $response = $this->readResponse();
        return isset($response[0]) && !isset($response[0]['!trap']);
    }

    /**
     * Déconnexion du routeur
     */
    public function disconnect() {
        if ($this->connection) {
            socket_close($this->connection);
            $this->connection = null;
            $this->log("Déconnexion du routeur");
        }
    }

    /**
     * Exécuter une commande sur le routeur
     * 
     * @param array $command Tableau de commandes
     * @return array Réponse du routeur
     */
    public function sendCommand($command) {
        if (!$this->connection && !$this->connect()) {
            throw new Exception("Non connecté au routeur Mikrotik");
        }
        
        // Envoi des commandes
        foreach ($command as $word) {
            $this->writeWord($word);
        }
        
        // Fin de commande
        $this->writeWord("");
        
        // Lecture de la réponse
        return $this->readResponse();
    }

    /**
     * Écrire un mot au format de l'API Mikrotik
     * 
     * @param string $word Mot à envoyer
     */
    private function writeWord($word) {
        $len = strlen($word);
        
        if ($len < 0x80) {
            $len = chr($len);
        } elseif ($len < 0x4000) {
            $len |= 0x8000;
            $len = chr(($len >> 8) & 0xFF) . chr($len & 0xFF);
        } elseif ($len < 0x200000) {
            $len |= 0xC00000;
            $len = chr(($len >> 16) & 0xFF) . chr(($len >> 8) & 0xFF) . chr($len & 0xFF);
        } elseif ($len < 0x10000000) {
            $len |= 0xE0000000;
            $len = chr(($len >> 24) & 0xFF) . chr(($len >> 16) & 0xFF) . chr(($len >> 8) & 0xFF) . chr($len & 0xFF);
        } elseif ($len >= 0x10000000) {
            $len = chr(0xF0) . chr(($len >> 24) & 0xFF) . chr(($len >> 16) & 0xFF) . chr(($len >> 8) & 0xFF) . chr($len & 0xFF);
        }
        
        socket_write($this->connection, $len . $word);
        $this->log(">> " . $word);
    }

    /**
     * Lire un mot depuis l'API Mikrotik
     * 
     * @return string Mot lu
     */
    private function readWord() {
        $retval = '';
        $len = ord(socket_read($this->connection, 1));
        
        if ($len & 0x80) {
            if (($len & 0xC0) == 0x80) {
                $len &= ~0x80;
                $len <<= 8;
                $len += ord(socket_read($this->connection, 1));
            } elseif (($len & 0xE0) == 0xC0) {
                $len &= ~0xC0;
                $len <<= 8;
                $len += ord(socket_read($this->connection, 1));
                $len <<= 8;
                $len += ord(socket_read($this->connection, 1));
            } elseif (($len & 0xF0) == 0xE0) {
                $len &= ~0xE0;
                $len <<= 8;
                $len += ord(socket_read($this->connection, 1));
                $len <<= 8;
                $len += ord(socket_read($this->connection, 1));
                $len <<= 8;
                $len += ord(socket_read($this->connection, 1));
            } elseif (($len & 0xF8) == 0xF0) {
                $len = ord(socket_read($this->connection, 1));
                $len <<= 8;
                $len += ord(socket_read($this->connection, 1));
                $len <<= 8;
                $len += ord(socket_read($this->connection, 1));
                $len <<= 8;
                $len += ord(socket_read($this->connection, 1));
            }
        }
        
        if ($len > 0) {
            $retval = socket_read($this->connection, $len);
        }
        
        $this->log("<< " . $retval);
        return $retval;
    }

    /**
     * Lire la réponse complète du routeur
     * 
     * @return array Réponse structurée
     */
    private function readResponse() {
        $response = array();
        $responseData = array();
        $receivedResponse = false;
        $endResponse = false;
        
        while (!$endResponse) {
            $word = $this->readWord();
            
            if ($word == '!done') {
                $receivedResponse = true;
            }
            
            if ($receivedResponse) {
                if ($word == '') {
                    $endResponse = true;
                } elseif ($word == '!trap') {
                    $responseData['!trap'] = true;
                } elseif ($word == '!done') {
                    $responseData['!done'] = true;
                } elseif (substr($word, 0, 1) == '=') {
                    $parts = explode('=', $word, 3);
                    if (isset($parts[2])) {
                        $responseData[$parts[1]] = $parts[2];
                    }
                } elseif (substr($word, 0, 1) == '!') {
                    $responseData[$word] = true;
                }
            } else {
                $parts = explode('=', $word, 3);
                if (isset($parts[1]) && isset($parts[2])) {
                    if (!isset($responseData[$parts[1]])) {
                        $responseData[$parts[1]] = $parts[2];
                    }
                }
            }
            
            if ($endResponse && count($responseData) > 0) {
                $response[] = $responseData;
                $responseData = array();
                $endResponse = false;
            }
            
            if ($word == '') {
                $endResponse = true;
            }
        }
        
        return $response;
    }

    /**
     * Enregistre un message de débogage
     * 
     * @param string $message Message à enregistrer
     */
    private function log($message) {
        if ($this->debug) {
            echo "[MikrotikAPI] " . date('Y-m-d H:i:s') . ": $message\n";
            // On peut aussi enregistrer dans un fichier
            // file_put_contents('mikrotik_api.log', "[".date('Y-m-d H:i:s')."] $message\n", FILE_APPEND);
        }
    }

    /**** FONCTIONS SPÉCIFIQUES POUR LA GESTION DU HOTSPOT ****/

    /**
     * Ajouter un utilisateur hotspot
     * 
     * @param string $username Nom d'utilisateur
     * @param string $password Mot de passe
     * @param string $profile Profil (optionnel)
     * @param string $comment Commentaire (optionnel)
     * @return array Résultat de l'opération
     */
    public function addHotspotUser($username, $password, $profile = null, $comment = null) {
        $command = ['/ip/hotspot/user/add', '=name=' . $username, '=password=' . $password];
        
        if ($profile) {
            $command[] = '=profile=' . $profile;
        }
        
        if ($comment) {
            $command[] = '=comment=' . $comment;
        }
        
        return $this->sendCommand($command);
    }

    /**
     * Générer un ticket hotspot avec un profil spécifique
     * 
     * @param string $profile Profil du ticket
     * @param int $count Nombre de tickets à générer
     * @param int $length Longueur du nom d'utilisateur
     * @param string $prefix Préfixe du nom d'utilisateur
     * @return array Liste des tickets générés
     */
    public function generateHotspotTickets($profile, $count = 1, $length = 6, $prefix = '') {
        $command = [
            '/ip/hotspot/user/add',
            '=count=' . $count,
            '=name-length=' . $length
        ];
        
        if (!empty($prefix)) {
            $command[] = '=name-prefix=' . $prefix;
        }
        
        if (!empty($profile)) {
            $command[] = '=profile=' . $profile;
        }
        
        return $this->sendCommand($command);
    }

    /**
     * Récupérer la liste des profils hotspot
     * 
     * @return array Liste des profils
     */
    public function getHotspotProfiles() {
        return $this->sendCommand(['/ip/hotspot/user/profile/print']);
    }

    /**
     * Récupérer la liste des utilisateurs hotspot
     * 
     * @return array Liste des utilisateurs
     */
    public function getHotspotUsers() {
        return $this->sendCommand(['/ip/hotspot/user/print']);
    }

    /**
     * Supprimer un utilisateur hotspot
     * 
     * @param string $username Nom d'utilisateur à supprimer
     * @return array Résultat de l'opération
     */
    public function removeHotspotUser($username) {
        // Trouver d'abord l'ID de l'utilisateur
        $users = $this->sendCommand(['/ip/hotspot/user/print', '?name=' . $username]);
        
        if (empty($users) || !isset($users[0]['.id'])) {
            return ['error' => 'Utilisateur non trouvé'];
        }
        
        $id = $users[0]['.id'];
        return $this->sendCommand(['/ip/hotspot/user/remove', '=.id=' . $id]);
    }

    /**
     * Récupérer les clients hotspot actuellement connectés
     * 
     * @return array Liste des clients connectés
     */
    public function getHotspotActiveUsers() {
        return $this->sendCommand(['/ip/hotspot/active/print']);
    }

    /**
     * Déconnecter un utilisateur hotspot actif
     * 
     * @param string $username Nom d'utilisateur à déconnecter
     * @return array Résultat de l'opération
     */
    public function disconnectHotspotUser($username) {
        // Trouver d'abord l'ID de l'utilisateur actif
        $users = $this->sendCommand(['/ip/hotspot/active/print', '?user=' . $username]);
        
        if (empty($users) || !isset($users[0]['.id'])) {
            return ['error' => 'Utilisateur actif non trouvé'];
        }
        
        $id = $users[0]['.id'];
        return $this->sendCommand(['/ip/hotspot/active/remove', '=.id=' . $id]);
    }

    /**
     * Obtenir des statistiques sur l'utilisation du hotspot
     * 
     * @return array Statistiques d'utilisation
     */
    public function getHotspotStatistics() {
        // Récupérer les statistiques d'host
        $hostStats = $this->sendCommand(['/ip/hotspot/host/print']);
        
        // Récupérer les statistiques du serveur
        $serverStats = $this->sendCommand(['/ip/hotspot/print']);
        
        // Récupérer les utilisateurs actifs
        $activeUsers = $this->getHotspotActiveUsers();
        
        return [
            'hosts' => $hostStats,
            'servers' => $serverStats,
            'active_users' => $activeUsers,
            'active_count' => count($activeUsers)
        ];
    }
}
?>
