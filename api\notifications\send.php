<?php
/**
 * send.php
 * 
 * API endpoint pour envoyer une not
// Utiliser le système CORS centralisé
require_once __DIR__ . '/../cors.php';

ification de ticket manuellement
 */

// Configuration pour assurer que les erreurs PHP sont retournées en JSON
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Inclusion de la fonction de log
require_once '../utils/logger.php';

// En-têtes CORS et JSON pour toutes les requêtes
// Retiré: // Retiré: // Commenté pour éviter duplication: // En-tête CORS géré par cors.php
// Retiré: // Retiré: // Commenté pour éviter duplication: header("Access-Control-Allow-Methods: POST, OPTIONS");
// Retiré: // Retiré: // Commenté pour éviter duplication: header("Access-Control-Max-Age: 3600");
// Retiré: // Retiré: // Commenté pour éviter duplication: header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Ajouter Content-Type uniquement pour les réponses non-OPTIONS
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    // Commenté pour éviter duplication: // Commenté pour éviter duplication: header("Content-Type: application/json; charset=UTF-8");
}

// Gestion spéciale pour les requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Vérifier si c'est une requête POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Méthode non autorisée. Utilisez POST.'
    ]);
    exit;
}

// Récupération des données
$jsonData = file_get_contents("php://input");
$data = json_decode($jsonData);

// Validation des données
if (!$data || !isset($data->notification_id)) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error', 
        'message' => 'Données incomplètes: notification_id requis'
    ]);
    logMessage("Données incomplètes pour l'envoi de notification", [
        'reçu' => json_decode($jsonData, true),
        'attendu' => 'notification_id'
    ], 'error');
    exit;
}

try {
    // Connexion à la base de données
    require_once '../config.php';
    
    // Récupérer les informations de la notification
    $stmt = $pdo->prepare("
        SELECT n.*, t.code, t.nom_client, t.email_client, t.telephone_client
        FROM notifications n
        JOIN tickets_internet t ON n.ticket_id = t.id
        WHERE n.id = ?
    ");
    $stmt->execute([$data->notification_id]);
    $notification = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$notification) {
        throw new Exception("Notification non trouvée");
    }
    
    // Vérifier si la notification est déjà envoyée
    if ($notification['status'] === 'sent') {
        echo json_encode([
            'status' => 'success',
            'message' => 'Notification déjà envoyée'
        ]);
        exit;
    }
    
    // Vérifier si nous avons un moyen de contacter le client
    $contactMethod = false;
    $sentVia = [];
    
    // Envoi par email si disponible
    if (!empty($notification['email_client'])) {
        try {
            // Inclusion de la librairie d'envoi d'emails
            require_once '../lib/mail_sender.php';
            
            // Adapter le sujet et le contenu selon le type de notification
            $subject = "Information concernant votre ticket Wifi";
            $content = $notification['message'];
            
            if ($notification['type'] === 'ticket_first_connection') {
                $subject = "Activation de votre ticket Wifi";
                if (empty($content)) {
                    $content = "Bonjour {$notification['nom_client']},\n\n";
                    $content .= "Votre ticket Wifi (code: {$notification['code']}) a été activé avec succès.\n";
                    $content .= "Vous pouvez maintenant profiter de votre connexion internet.\n\n";
                    $content .= "Cordialement,\nL'équipe du Moulin";
                }
            } else if ($notification['type'] === 'ticket_expiration') {
                $subject = "Expiration prochaine de votre ticket Wifi";
                if (empty($content)) {
                    $content = "Bonjour {$notification['nom_client']},\n\n";
                    $content .= "Votre ticket Wifi (code: {$notification['code']}) expire bientôt.\n";
                    $content .= "Pour continuer à bénéficier de votre connexion internet, veuillez renouveler votre ticket.\n\n";
                    $content .= "Cordialement,\nL'équipe du Moulin";
                }
            }
            
            $mailSent = sendMail($notification['email_client'], $subject, $content);
            
            if ($mailSent) {
                $contactMethod = true;
                $sentVia[] = 'email';
                logMessage("Email de notification envoyé", [
                    'to' => $notification['email_client'],
                    'subject' => $subject,
                    'notification_id' => $data->notification_id
                ]);
            }
        } catch (Exception $mailError) {
            logMessage("Erreur lors de l'envoi d'email", [
                'error' => $mailError->getMessage(),
                'to' => $notification['email_client'],
                'notification_id' => $data->notification_id
            ], 'error');
        }
    }
    
    // Envoi par SMS si disponible
    if (!empty($notification['telephone_client'])) {
        try {
            // Inclusion de la librairie d'envoi de SMS
            require_once '../lib/sms_sender.php';
            
            // Préparer le message SMS (plus court que l'email)
            $message = $notification['message'];
            
            if ($notification['type'] === 'ticket_first_connection') {
                if (empty($message)) {
                    $message = "Ticket Wifi {$notification['code']} activé avec succès. Bonne navigation!";
                }
            } else if ($notification['type'] === 'ticket_expiration') {
                if (empty($message)) {
                    $message = "Votre ticket Wifi {$notification['code']} expire bientôt. Pensez à le renouveler.";
                }
            }
            
            // Limiter la longueur du message SMS
            if (strlen($message) > 160) {
                $message = substr($message, 0, 157) . '...';
            }
            
            $smsSent = sendSms($notification['telephone_client'], $message);
            
            if ($smsSent) {
                $contactMethod = true;
                $sentVia[] = 'sms';
                logMessage("SMS de notification envoyé", [
                    'to' => $notification['telephone_client'],
                    'notification_id' => $data->notification_id
                ]);
            }
        } catch (Exception $smsError) {
            logMessage("Erreur lors de l'envoi de SMS", [
                'error' => $smsError->getMessage(),
                'to' => $notification['telephone_client'],
                'notification_id' => $data->notification_id
            ], 'error');
        }
    }
    
    // Mettre à jour le statut de la notification
    $newStatus = $contactMethod ? 'sent' : 'failed';
    $stmt = $pdo->prepare("
        UPDATE notifications 
        SET status = ?, 
            sent_via = ?, 
            sent_at = NOW(),
            updated_at = NOW()
        WHERE id = ?
    ");
    $stmt->execute([
        $newStatus,
        $contactMethod ? implode(',', $sentVia) : null,
        $data->notification_id
    ]);
    
    if ($contactMethod) {
        // Réponse de succès
        echo json_encode([
            'status' => 'success',
            'message' => 'Notification envoyée avec succès',
            'sent_via' => $sentVia
        ]);
    } else {
        // Réponse d'avertissement si aucun moyen de contact n'est disponible
        echo json_encode([
            'status' => 'warning',
            'message' => 'Aucun moyen de contact (email/téléphone) n\'est disponible pour ce client'
        ]);
    }
    
} catch (Exception $e) {
    // Journaliser l'erreur
    logMessage("Erreur lors de l'envoi de la notification", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'notification_id' => isset($data->notification_id) ? $data->notification_id : 'unknown'
    ], 'error');
    
    // Mettre à jour le statut de la notification en cas d'erreur
    if (isset($data->notification_id)) {
        try {
            $stmt = $pdo->prepare("
                UPDATE notifications 
                SET status = 'failed',
                    updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$data->notification_id]);
        } catch (Exception $updateError) {
            logMessage("Impossible de mettre à jour le statut de la notification", [
                'error' => $updateError->getMessage(),
                'notification_id' => $data->notification_id
            ], 'error');
        }
    }
    
    // Réponse d'erreur
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Erreur lors de l\'envoi de la notification: ' . $e->getMessage()
    ]);
}
?>
