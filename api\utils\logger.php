<?php
/**
 * logger.php
 * 
 * Système de logging simple pour l'application
 */

/**
 * Enregistre un message dans le fichier de log
 * 
 * @param string $message Message à enregistrer
 * @param array $context Données contextuelles
 * @param string $level Niveau de log (info, warning, error)
 */
function logMessage($message, $context = [], $level = 'info') {
    $logFile = __DIR__ . '/../logs/app.log';
    
    // Créer le répertoire logs s'il n'existe pas
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    // Préparer le message de log
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' | Context: ' . json_encode($context) : '';
    $logEntry = "[{$timestamp}] [{$level}] {$message}{$contextStr}" . PHP_EOL;
    
    // Écrire dans le fichier de log
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    
    // En mode développement, aussi afficher dans la console PHP
    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE && $level === 'error') {
        error_log($logEntry);
    }
}
