<?php
/**
 * Fonctions pour la gestion des événements associés aux moulins
 * 
 * Ces fonctions permettent d'ajouter, récupérer, mettre à jour et supprimer des événements
 * associés aux moulins, comme les maintenances, pannes, réparations, etc.
 */

require_once dirname(__DIR__) . '/config.php';

/**
 * Ajoute un événement à un moulin
 * 
 * @param array $evenementData Les données de l'événement à ajouter
 * @return int|false L'ID de l'événement ajouté ou false en cas d'échec
 */
function ajouterEvenementMoulin($evenementData) {
    global $pdo;
    
    try {
        // Vérifier que les données requises sont présentes
        if (!isset($evenementData['moulin_id']) || 
            !isset($evenementData['user_id']) || 
            !isset($evenementData['type']) || 
            !isset($evenementData['description']) || 
            !isset($evenementData['date_debut'])) {
            return false;
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO moulin_evenements 
            (moulin_id, user_id, type, description, date_debut, date_fin, cout, statut)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            $evenementData['moulin_id'],
            $evenementData['user_id'],
            $evenementData['type'],
            $evenementData['description'],
            $evenementData['date_debut'],
            $evenementData['date_fin'] ?? null,
            $evenementData['cout'] ?? null,
            $evenementData['statut'] ?? 'en_cours'
        ]);
        
        if ($result) {
            return $pdo->lastInsertId();
        }
        
        return false;
    } catch (PDOException $e) {
        logError("Erreur lors de l'ajout de l'événement", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return false;
    }
}

/**
 * Récupère tous les événements associés à un moulin
 * 
 * @param int $moulinId L'ID du moulin
 * @param array $filtres Filtres optionnels (type, statut, date_debut, date_fin)
 * @return array La liste des événements associés
 */
function recupererEvenementsMoulin($moulinId, $filtres = []) {
    global $pdo;
    
    try {
        $sql = "
            SELECT 
                me.*,
                u.nom as user_nom,
                u.prenom as user_prenom,
                m.nom as moulin_nom
            FROM 
                moulin_evenements me
            JOIN 
                users u ON me.user_id = u.id
            JOIN 
                moulins m ON me.moulin_id = m.id
            WHERE 
                me.moulin_id = ?
        ";
        
        $params = [$moulinId];
        
        // Ajouter les filtres optionnels
        if (isset($filtres['type']) && $filtres['type']) {
            $sql .= " AND me.type = ?";
            $params[] = $filtres['type'];
        }
        
        if (isset($filtres['statut']) && $filtres['statut']) {
            $sql .= " AND me.statut = ?";
            $params[] = $filtres['statut'];
        }
        
        if (isset($filtres['date_debut']) && $filtres['date_debut']) {
            $sql .= " AND me.date_debut >= ?";
            $params[] = $filtres['date_debut'];
        }
        
        if (isset($filtres['date_fin']) && $filtres['date_fin']) {
            $sql .= " AND (me.date_fin <= ? OR me.date_fin IS NULL)";
            $params[] = $filtres['date_fin'];
        }
        
        $sql .= " ORDER BY me.date_debut DESC";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        logError("Erreur lors de la récupération des événements", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return [];
    }
}

/**
 * Met à jour un événement associé à un moulin
 * 
 * @param int $evenementId L'ID de l'événement à mettre à jour
 * @param array $evenementData Les nouvelles données de l'événement
 * @return bool True si la mise à jour a réussi, false sinon
 */
function mettreAJourEvenementMoulin($evenementId, $evenementData) {
    global $pdo;
    
    try {
        // Construire la requête de mise à jour dynamiquement
        $fields = [];
        $params = [];
        
        foreach ($evenementData as $field => $value) {
            // Éviter les champs qui ne devraient pas être mis à jour
            if (!in_array($field, ['id', 'moulin_id', 'created_at'])) {
                $fields[] = "$field = ?";
                $params[] = $value;
            }
        }
        
        if (empty($fields)) {
            return false;
        }
        
        $sql = "UPDATE moulin_evenements SET " . implode(", ", $fields) . " WHERE id = ?";
        $params[] = $evenementId;
        
        $stmt = $pdo->prepare($sql);
        return $stmt->execute($params);
        
    } catch (PDOException $e) {
        logError("Erreur lors de la mise à jour de l'événement", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return false;
    }
}

/**
 * Clôture un événement (marque comme terminé et ajoute une date de fin)
 * 
 * @param int $evenementId L'ID de l'événement à clôturer
 * @param array $donneesCloture Les données de clôture (date_fin, cout, description additionnelle)
 * @return bool True si la clôture a réussi, false sinon
 */
function cloturerEvenementMoulin($evenementId, $donneesCloture) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            UPDATE moulin_evenements
            SET statut = 'termine',
                date_fin = ?,
                cout = ?,
                description = CONCAT(description, '\n\nRésolution: ', ?)
            WHERE id = ? AND statut = 'en_cours'
        ");
        
        return $stmt->execute([
            $donneesCloture['date_fin'] ?? date('Y-m-d H:i:s'),
            $donneesCloture['cout'] ?? null,
            $donneesCloture['resolution'] ?? '',
            $evenementId
        ]);
        
    } catch (PDOException $e) {
        logError("Erreur lors de la clôture de l'événement", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return false;
    }
}

/**
 * Annule un événement en cours
 * 
 * @param int $evenementId L'ID de l'événement à annuler
 * @param string $raisonAnnulation La raison de l'annulation
 * @return bool True si l'annulation a réussi, false sinon
 */
function annulerEvenementMoulin($evenementId, $raisonAnnulation = '') {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            UPDATE moulin_evenements
            SET statut = 'annule',
                date_fin = NOW(),
                description = CONCAT(description, '\n\nAnnulation: ', ?)
            WHERE id = ? AND statut = 'en_cours'
        ");
        
        return $stmt->execute([
            $raisonAnnulation,
            $evenementId
        ]);
        
    } catch (PDOException $e) {
        logError("Erreur lors de l'annulation de l'événement", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return false;
    }
}

/**
 * Récupère les statistiques des événements pour un moulin
 * 
 * @param int $moulinId L'ID du moulin
 * @param string $periode 'mensuel', 'trimestriel', 'annuel'
 * @return array Les statistiques des événements
 */
function statistiquesEvenementsMoulin($moulinId, $periode = 'mensuel') {
    global $pdo;
    
    try {
        // Définir la période de regroupement
        switch ($periode) {
            case 'trimestriel':
                $groupBy = "CONCAT(YEAR(date_debut), '-Q', QUARTER(date_debut))";
                $label = "CONCAT('T', QUARTER(date_debut), ' ', YEAR(date_debut))";
                break;
            case 'annuel':
                $groupBy = "YEAR(date_debut)";
                $label = "YEAR(date_debut)";
                break;
            case 'mensuel':
            default:
                $groupBy = "DATE_FORMAT(date_debut, '%Y-%m')";
                $label = "DATE_FORMAT(date_debut, '%m/%Y')";
                break;
        }
        
        // Requête pour obtenir les statistiques par période et par type
        $sql = "
            SELECT 
                $label as periode,
                type,
                COUNT(*) as nombre,
                AVG(TIMESTAMPDIFF(HOUR, date_debut, IFNULL(date_fin, NOW()))) as duree_moyenne_heures,
                SUM(cout) as cout_total
            FROM 
                moulin_evenements
            WHERE 
                moulin_id = ?
            GROUP BY 
                $groupBy, type
            ORDER BY 
                MIN(date_debut) DESC, type
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$moulinId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (PDOException $e) {
        logError("Erreur lors de la récupération des statistiques d'événements", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return [];
    }
}

/**
 * Récupère un événement spécifique par son ID
 * 
 * @param int $evenementId L'ID de l'événement
 * @return array|false Les données de l'événement ou false s'il n'existe pas
 */
function recupererEvenementParId($evenementId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                me.*,
                u.nom as user_nom,
                u.prenom as user_prenom,
                m.nom as moulin_nom
            FROM 
                moulin_evenements me
            JOIN 
                users u ON me.user_id = u.id
            JOIN 
                moulins m ON me.moulin_id = m.id
            WHERE 
                me.id = ?
        ");
        
        $stmt->execute([$evenementId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        logError("Erreur lors de la récupération de l'événement", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return false;
    }
}

/**
 * Fonction pour enregistrer les erreurs dans un fichier de log
 * 
 * @param string $message Le message d'erreur
 * @param array $context Les données contextuelles de l'erreur
 */
function logError($message, $context = []) {
    $logFile = dirname(__DIR__) . '/logs/evenements.log';
    $logDir = dirname($logFile);
    
    // Créer le répertoire des logs s'il n'existe pas
    if (!is_dir($logDir)) {
        mkdir($logDir, 0777, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $contextData = !empty($context) ? ' - ' . json_encode($context) : '';
    
    file_put_contents(
        $logFile, 
        "[{$timestamp}] {$message}{$contextData}\n", 
        FILE_APPEND
    );
}
