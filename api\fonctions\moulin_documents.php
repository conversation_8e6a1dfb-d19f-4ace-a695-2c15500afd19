<?php
/**
 * Fonctions pour la gestion des documents associés aux moulins
 * 
 * Ces fonctions permettent d'ajouter, récupérer, et supprimer des documents
 * associés aux moulins, ainsi que de gérer l'upload des fichiers.
 */

require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/vendor/autoload.php';

/**
 * Ajoute un document à un moulin dans la base de données
 * 
 * @param array $documentData Les données du document à ajouter
 * @return int|false L'ID du document ajouté ou false en cas d'échec
 */
function ajouterDocumentMoulin($documentData) {
    global $pdo;
    
    try {
        // Vérifier que les données requises sont présentes
        if (!isset($documentData['moulin_id']) || 
            !isset($documentData['document_type_id']) || 
            !isset($documentData['titre']) || 
            !isset($documentData['chemin_fichier'])) {
            return false;
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO moulin_documents 
            (moulin_id, document_type_id, titre, chemin_fichier, description, date_ajout)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $result = $stmt->execute([
            $documentData['moulin_id'],
            $documentData['document_type_id'],
            $documentData['titre'],
            $documentData['chemin_fichier'],
            $documentData['description'] ?? null
        ]);
        
        if ($result) {
            return $pdo->lastInsertId();
        }
        
        return false;
    } catch (PDOException $e) {
        logError("Erreur lors de l'ajout du document", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return false;
    }
}

/**
 * Récupère tous les documents associés à un moulin
 * 
 * @param int $moulinId L'ID du moulin
 * @return array La liste des documents associés
 */
function recupererDocumentsMoulin($moulinId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT 
                md.id, md.titre, md.description, md.chemin_fichier, md.date_ajout,
                dt.nom as type_document
            FROM 
                moulin_documents md
            JOIN 
                document_types dt ON md.document_type_id = dt.id
            WHERE 
                md.moulin_id = ?
            ORDER BY 
                md.date_ajout DESC
        ");

        $stmt->execute([$moulinId]);
        $documents = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Ajouter l'URL complète pour chaque document
        $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'];
        $apiPath = dirname($_SERVER['PHP_SELF']); // ex: /api/fonctions
        $baseApiPath = dirname($apiPath); // ex: /api

        foreach ($documents as &$doc) {
            $doc['url'] = $baseUrl . $baseApiPath . '/' . $doc['chemin_fichier'];
        }

        return $documents;
    } catch (PDOException $e) {
        logError("Erreur lors de la récupération des documents", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return [];
    }
}

/**
 * Supprime un document associé à un moulin
 * 
 * @param int $documentId L'ID du document à supprimer
 * @return bool True si la suppression a réussi, false sinon
 */
function supprimerDocumentMoulin($documentId) {
    global $pdo;
    
    try {
        // Récupérer d'abord le chemin du fichier pour le supprimer
        $stmt = $pdo->prepare("SELECT chemin_fichier FROM moulin_documents WHERE id = ?");
        $stmt->execute([$documentId]);
        $document = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$document) {
            return false;
        }
        
        // Supprimer le fichier physique
        // Reconstruire le chemin absolu du fichier pour le supprimer
        $absolutePath = dirname(__DIR__) . DIRECTORY_SEPARATOR . str_replace('/', DIRECTORY_SEPARATOR, $document['chemin_fichier']);
        if (file_exists($absolutePath)) {
            unlink($absolutePath);
        }
        
        // Supprimer l'enregistrement de la base de données
        $stmt = $pdo->prepare("DELETE FROM moulin_documents WHERE id = ?");
        return $stmt->execute([$documentId]);
        
    } catch (PDOException $e) {
        logError("Erreur lors de la suppression du document", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return false;
    }
}

/**
 * Gère l'upload d'un fichier et l'associe à un moulin
 * 
 * @param array $uploadedFile Les données du fichier uploadé ($_FILES['nom_du_champ'])
 * @param array $documentData Les données du document à associer au moulin
 * @param string $uploadDir Le répertoire d'upload (optionnel)
 * @return array Résultat de l'opération
 */
function uploadEtAssocierDocument($uploadedFile, $documentData, $uploadDir = null) {
    try {
        // Vérifier le fichier uploadé
        if ($uploadedFile['error'] !== UPLOAD_ERR_OK) {
            return [
                'success' => false,
                'message' => 'Erreur lors de l\'upload du fichier: ' . uploadErrorMessage($uploadedFile['error'])
            ];
        }
        
        // Définir le répertoire d'upload si non fourni
        if ($uploadDir === null) {
            $uploadDir = dirname(__DIR__) . '/uploads/moulins/' . $documentData['moulin_id'];
        }
        
        // Créer le répertoire si nécessaire
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }
        
        // Générer un nom de fichier unique pour éviter les collisions
        $extension = pathinfo($uploadedFile['name'], PATHINFO_EXTENSION);
        $nomFichier = uniqid('doc_') . '_' . time() . '.' . $extension;
        $cheminComplet = $uploadDir . '/' . $nomFichier;
        
        // Déplacer le fichier uploadé vers sa destination finale
        if (!move_uploaded_file($uploadedFile['tmp_name'], $cheminComplet)) {
            // En mode test, si ce n'est pas un upload réel via le formulaire
            if (is_file($uploadedFile['tmp_name'])) {
                copy($uploadedFile['tmp_name'], $cheminComplet);
            } else {
                return [
                    'success' => false,
                    'message' => 'Impossible de déplacer le fichier uploadé'
                ];
            }
        }
        
        // Chemin relatif à stocker en base de données
        $relativePath = 'uploads/moulins/' . $documentData['moulin_id'] . '/' . $nomFichier;
        $documentData['chemin_fichier'] = $relativePath;
        
        // Ajouter le document en base de données
        $documentId = ajouterDocumentMoulin($documentData);
        
        if (!$documentId) {
            // En cas d'échec, supprimer le fichier uploadé
            if (file_exists($cheminComplet)) {
                unlink($cheminComplet);
            }
            
            return [
                'success' => false,
                'message' => 'Erreur lors de l\'ajout du document en base de données'
            ];
        }
        
        return [
            'success' => true,
            'document_id' => $documentId,
            'chemin_fichier' => $cheminComplet,
            'nom_fichier' => $nomFichier
        ];
        
    } catch (Exception $e) {
        logError("Erreur lors de l'upload et l'association du document", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        
        return [
            'success' => false,
            'message' => 'Une erreur inattendue est survenue: ' . $e->getMessage()
        ];
    }
}

/**
 * Récupère les types de documents disponibles
 * 
 * @return array La liste des types de documents
 */
function recupererTypesDocuments() {
    global $pdo;
    
    try {
        $stmt = $pdo->query("SELECT id, nom, description FROM document_types ORDER BY nom");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        logError("Erreur lors de la récupération des types de documents", [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return [];
    }
}

/**
 * Convertit le code d'erreur d'upload en message lisible
 * 
 * @param int $errorCode Le code d'erreur d'upload
 * @return string Le message d'erreur correspondant
 */
function uploadErrorMessage($errorCode) {
    switch ($errorCode) {
        case UPLOAD_ERR_INI_SIZE:
            return 'Le fichier dépasse la taille maximale autorisée par PHP';
        case UPLOAD_ERR_FORM_SIZE:
            return 'Le fichier dépasse la taille maximale autorisée par le formulaire';
        case UPLOAD_ERR_PARTIAL:
            return 'Le fichier n\'a été que partiellement uploadé';
        case UPLOAD_ERR_NO_FILE:
            return 'Aucun fichier n\'a été uploadé';
        case UPLOAD_ERR_NO_TMP_DIR:
            return 'Le dossier temporaire est manquant';
        case UPLOAD_ERR_CANT_WRITE:
            return 'Impossible d\'écrire le fichier sur le disque';
        case UPLOAD_ERR_EXTENSION:
            return 'Une extension PHP a arrêté l\'upload du fichier';
        default:
            return 'Erreur inconnue';
    }
}

/**
 * Fonction pour enregistrer les erreurs dans un fichier de log
 * 
 * @param string $message Le message d'erreur
 * @param array $context Les données contextuelles de l'erreur
 */
/**
 * Gère le téléchargement d'un document, avec conversion en PDF si nécessaire.
 * 
 * @param int $documentId L'ID du document à télécharger.
 */
function telechargerDocumentEnPdf($documentId) {
    global $pdo;

    try {
        // Récupérer le moulin_id pour construire un chemin fiable
        $stmt = $pdo->prepare("SELECT chemin_fichier, titre, moulin_id FROM moulin_documents WHERE id = ?");
        $stmt->execute([$documentId]);
        $document = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$document) {
            send_json(['error' => 'Document non trouvé en base de données'], 404);
        }

        // Reconstruire le chemin attendu pour plus de robustesse
        $filename = basename($document['chemin_fichier']);
        $expectedPath = 'uploads' . DIRECTORY_SEPARATOR . 'moulins' . DIRECTORY_SEPARATOR . $document['moulin_id'] . DIRECTORY_SEPARATOR . $filename;
        $absolutePath = dirname(__DIR__) . DIRECTORY_SEPARATOR . $expectedPath;

        logError("Tentative de téléchargement de fichier", [
            'document_id' => $documentId,
            'original_path' => $document['chemin_fichier'],
            'reconstructed_path' => $absolutePath,
            'exists' => file_exists($absolutePath) ? 'oui' : 'non'
        ]);

        if (!file_exists($absolutePath)) {
            send_json(['error' => 'Fichier non trouvé sur le serveur'], 404);
        }

        $fileExtension = strtolower(pathinfo($absolutePath, PATHINFO_EXTENSION));
        $newFilename = preg_replace('/\.[^.\s]{3,4}$/', '', $document['titre']) . '.pdf';

        if ($fileExtension === 'pdf') {
            // Servir le PDF directement
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $newFilename . '"');
            readfile($absolutePath);
            exit;
        } elseif (in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif'])) {
            // Convertir l'image en PDF
            $pdf = new TCPDF();
            $pdf->SetCreator(PDF_CREATOR);
            $pdf->SetAuthor('Gestion Moulin');
            $pdf->SetTitle($document['titre']);
            $pdf->SetSubject('Document Moulin');
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);
            $pdf->AddPage();
            
            // Obtenir les dimensions de l'image pour ajuster la page
            list($width, $height) = getimagesize($absolutePath);
            $orientation = ($width > $height) ? 'L' : 'P';
            $pdf->setPageOrientation($orientation);

            $pdf->Image($absolutePath, 0, 0, $pdf->getPageWidth(), 0, '', '', '', false, 300, '', false, false, 0);

            $pdf->Output($newFilename, 'D');
            exit;
        } else {
            // Pour les autres types de fichiers, on les sert tel quel pour le moment
            // Une conversion (ex: DOCX -> PDF) nécessiterait un outil plus puissant comme LibreOffice
            header('Content-Type: ' . mime_content_type($absolutePath));
            header('Content-Disposition: attachment; filename="' . basename($absolutePath) . '"');
            readfile($absolutePath);
            exit;
        }

    } catch (Exception $e) {
        logError("Erreur lors du téléchargement du document", ['message' => $e->getMessage()]);
        send_json(['error' => 'Erreur serveur lors de la préparation du téléchargement.'], 500);
    }
}

function logError($message, $context = []) {
    $logFile = dirname(__DIR__) . '/logs/documents.log';
    $logDir = dirname($logFile);
    
    // Créer le répertoire des logs s'il n'existe pas
    if (!is_dir($logDir)) {
        mkdir($logDir, 0777, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $contextData = !empty($context) ? ' - ' . json_encode($context) : '';
    
    file_put_contents(
        $logFile, 
        "[{$timestamp}] {$message}{$contextData}\n", 
        FILE_APPEND
    );
}
