﻿// Configuration globale de l'application

// URL de base de l'API - Configuration avec proxy Vite
// Cette URL utilise le proxy Vite pour rediriger vers l'API PHP
const getApiBaseUrl = () => {
  // VÃ©rifier si nous sommes dans un environnement navigateur
  if (typeof window === 'undefined') {
    // En mode SSR ou build, utiliser l'URL par dÃ©faut
    return 'http://localhost/Gestion_moulin_wifiZone_ok/api';
  }
  
  // En dÃ©veloppement avec Vite (port 3000 ou 3001), utiliser le proxy
  if (window.location.port === '8080') {
    return '/api';
  }
  
  // En dÃ©veloppement direct ou production, utiliser l'URL complÃ¨te
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return 'http://localhost/Gestion_moulin_wifiZone_ok/api';
  }
  
  // En production, construire l'URL basÃ©e sur l'hÃ´te actuel
  const { protocol, hostname } = window.location;
  return `${protocol}//${hostname}/Gestion_moulin_wifiZone_ok/api`;
};

// URL de base de l'API
export const API_BASE_URL = getApiBaseUrl();

// Configuration de l'application
export const APP_CONFIG = {
  API_BASE_URL,
  API_TIMEOUT: 15000,
  JWT_STORAGE_KEY: 'authToken',
  USER_DATA_STORAGE_KEY: 'userData'
};

// Log de configuration seulement dans le navigateur
if (typeof window !== 'undefined') {
  console.log('Configuration API initialisÃ©e:', {
    API_BASE_URL,
    hostname: window.location.hostname,
    port: window.location.port
  });
}

