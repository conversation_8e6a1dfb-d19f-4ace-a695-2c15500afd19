<?php
/**
 * Middleware d'authentification JWT pour vérifier les droits d'accès
 */

require_once __DIR__ . '/../vendor/autoload.php';
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class JWTAuthMiddleware {
    private $secretKey;
    
    public function __construct() {
        // Utiliser la même clé que dans config.php
        $this->secretKey = 'ef26b726787e93dab2ea';
    }
    
    /**
     * Vérifie si l'utilisateur est connecté via JWT
     * @return bool
     */
    public function isAuthenticated() {
        $token = $this->getTokenFromHeaders();
        
        if (!$token) {
            error_log("[JWT_AUTH] Aucun token trouvé dans les en-têtes");
            return false;
        }
        
        try {
            $decoded = JWT::decode($token, new Key($this->secretKey, 'HS256'));
            error_log("[JWT_AUTH] Token décodé avec succès - User ID: " . $decoded->data->id);
            return true;
        } catch (Exception $e) {
            error_log("[JWT_AUTH] Erreur de décodage du token: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Vérifie si l'utilisateur est un administrateur
     * @return bool
     */
    public function isAdmin() {
        $token = $this->getTokenFromHeaders();
        
        if (!$token) {
            error_log("[JWT_AUTH] Aucun token pour vérification admin");
            return false;
        }
        
        try {
            $decoded = JWT::decode($token, new Key($this->secretKey, 'HS256'));
            $role = $decoded->data->role ?? '';
            
            error_log("[JWT_AUTH] Vérification admin - User ID: {$decoded->data->id}, Role: {$role}");
            
            return $role === 'admin';
        } catch (Exception $e) {
            error_log("[JWT_AUTH] Erreur lors de la vérification admin: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Récupère l'ID de l'utilisateur connecté
     * @return int|null
     */
    public function getUserId() {
        $token = $this->getTokenFromHeaders();
        
        if (!$token) {
            return null;
        }
        
        try {
            $decoded = JWT::decode($token, new Key($this->secretKey, 'HS256'));
            return $decoded->data->id ?? null;
        } catch (Exception $e) {
            error_log("[JWT_AUTH] Erreur lors de la récupération de l'ID utilisateur: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Récupère les données complètes de l'utilisateur depuis le token
     * @return object|null
     */
    public function getUserData() {
        $token = $this->getTokenFromHeaders();
        
        if (!$token) {
            return null;
        }
        
        try {
            $decoded = JWT::decode($token, new Key($this->secretKey, 'HS256'));
            return $decoded->data ?? null;
        } catch (Exception $e) {
            error_log("[JWT_AUTH] Erreur lors de la récupération des données utilisateur: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Extrait le token JWT des en-têtes HTTP
     * @return string|null
     */
    private function getTokenFromHeaders() {
        $headers = getallheaders();
        
        // Vérifier différents formats d'en-têtes
        $authHeader = null;
        if (isset($headers['Authorization'])) {
            $authHeader = $headers['Authorization'];
        } elseif (isset($headers['authorization'])) {
            $authHeader = $headers['authorization'];
        }
        
        if (!$authHeader) {
            error_log("[JWT_AUTH] Aucun en-tête Authorization trouvé");
            error_log("[JWT_AUTH] En-têtes disponibles: " . json_encode(array_keys($headers)));
            return null;
        }
        
        // Format attendu: "Bearer [token]"
        if (strpos($authHeader, 'Bearer ') === 0) {
            $token = substr($authHeader, 7);
            error_log("[JWT_AUTH] Token extrait: " . substr($token, 0, 20) . "...");
            return $token;
        }
        
        error_log("[JWT_AUTH] Format d'en-tête Authorization invalide: " . $authHeader);
        return null;
    }
    
    /**
     * Vérifie si le token est valide et non expiré
     * @return bool
     */
    public function isTokenValid() {
        $token = $this->getTokenFromHeaders();
        
        if (!$token) {
            return false;
        }
        
        try {
            $decoded = JWT::decode($token, new Key($this->secretKey, 'HS256'));
            
            // Vérifier l'expiration
            $now = time();
            if (isset($decoded->exp) && $decoded->exp < $now) {
                error_log("[JWT_AUTH] Token expiré");
                return false;
            }
            
            return true;
        } catch (Exception $e) {
            error_log("[JWT_AUTH] Token invalide: " . $e->getMessage());
            return false;
        }
    }
}
?>
