-- Module <PERSON>ystème d'Alertes Dynamiques
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des types d'alertes
CREATE TABLE IF NOT EXISTS `types_alertes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL,
  `nom` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `niveau` enum('info','attention','urgent','critique') NOT NULL DEFAULT 'info',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des alertes
CREATE TABLE IF NOT EXISTS `alertes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type_id` int(11) NOT NULL,
  `moulin_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `message` text NOT NULL,
  `details` text DEFAULT NULL,
  `date_declenchement` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_resolution` datetime DEFAULT NULL,
  `statut` enum('active','en_traitement','resolue','ignoree') NOT NULL DEFAULT 'active',
  `traite_par` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`type_id`) REFERENCES `types_alertes` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`traite_par`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des conditions de déclenchement
CREATE TABLE IF NOT EXISTS `conditions_alertes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type_id` int(11) NOT NULL,
  `parametrage` json NOT NULL COMMENT 'Configuration JSON des conditions de déclenchement',
  `actif` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`type_id`) REFERENCES `types_alertes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des notifications d'alertes
CREATE TABLE IF NOT EXISTS `notifications_alertes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `alerte_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `canal` enum('email','sms','systeme') NOT NULL DEFAULT 'systeme',
  `statut` enum('en_attente','envoyee','echec','lue') NOT NULL DEFAULT 'en_attente',
  `date_envoi` datetime DEFAULT NULL,
  `date_lecture` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`alerte_id`) REFERENCES `alertes` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertion des types d'alertes par défaut
INSERT IGNORE INTO `types_alertes` (`code`, `nom`, `description`, `niveau`) VALUES
('STOCK_BAS', 'Stock de tickets bas', 'Alerte lorsque le stock de tickets est faible', 'attention'),
('PANNE_MOULIN', 'Panne de moulin', 'Alerte lors d''une panne d''un moulin', 'urgent'),
('INACTIVITE', 'Inactivité détectée', 'Alerte lorsqu''un moulin n''a pas eu d''activité depuis longtemps', 'attention'),
('PERFORMANCE_BASSE', 'Performance faible', 'Alerte lorsque les performances sont inférieures aux objectifs', 'info'),
('MAINTENANCE_REQUISE', 'Maintenance requise', 'Alerte pour maintenance préventive programmée', 'attention'),
('TENTATIVE_ACCES', 'Tentatives d''accès non autorisées', 'Alerte de sécurité pour tentatives d''accès non autorisées', 'critique');

-- Insertion de quelques conditions d'alertes par défaut
INSERT IGNORE INTO `conditions_alertes` (`type_id`, `parametrage`, `actif`)
SELECT ta.id, '{"seuil": 10, "verification_intervalle": "daily"}', 1
FROM `types_alertes` ta WHERE ta.code = 'STOCK_BAS';

INSERT IGNORE INTO `conditions_alertes` (`type_id`, `parametrage`, `actif`)
SELECT ta.id, '{"jours_inactivite": 3, "verification_intervalle": "daily"}', 1
FROM `types_alertes` ta WHERE ta.code = 'INACTIVITE';

INSERT IGNORE INTO `conditions_alertes` (`type_id`, `parametrage`, `actif`)
SELECT ta.id, '{"seuil_performance": 70, "periode": "monthly"}', 1
FROM `types_alertes` ta WHERE ta.code = 'PERFORMANCE_BASSE';
