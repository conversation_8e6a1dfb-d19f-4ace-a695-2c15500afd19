<?php
/**
 * Fonctions de calcul pour le modèle Moulin
 * 
 * Ces fonctions permettent de calculer différentes métriques liées à la 
 * production et à la rentabilité des moulins.
 */

require_once dirname(__DIR__) . '/config.php';

/**
 * Calcule les recettes totales d'un moulin sur une période donnée
 * 
 * @param int $moulinId L'identifiant du moulin
 * @param string $dateDebut Date de début au format Y-m-d
 * @param string $dateFin Date de fin au format Y-m-d
 * @return float Le montant total des recettes
 */
function calculRecettesTotales($moulinId, $dateDebut, $dateFin) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT SUM(montant) AS total
        FROM recettes 
        WHERE moulin_id = ? 
        AND date BETWEEN ? AND ?
    ");
    
    $stmt->execute([$moulinId, $dateDebut, $dateFin]);
    $resultat = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return floatval($resultat['total'] ?? 0);
}

/**
 * Calcule les charges totales d'un moulin sur une période donnée
 * 
 * @param int $moulinId L'identifiant du moulin
 * @param string $dateDebut Date de début au format Y-m-d
 * @param string $dateFin Date de fin au format Y-m-d
 * @return float Le montant total des charges
 */
function calculChargesTotales($moulinId, $dateDebut, $dateFin) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT SUM(montant) AS total
        FROM charges 
        WHERE moulin_id = ? 
        AND date BETWEEN ? AND ?
    ");
    
    $stmt->execute([$moulinId, $dateDebut, $dateFin]);
    $resultat = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return floatval($resultat['total'] ?? 0);
}

/**
 * Calcule la rentabilité d'un moulin sur une période donnée
 * 
 * @param int $moulinId L'identifiant du moulin
 * @param string $dateDebut Date de début au format Y-m-d
 * @param string $dateFin Date de fin au format Y-m-d
 * @return array Tableau contenant le bénéfice et la marge de rentabilité
 */
function calculRentabilite($moulinId, $dateDebut, $dateFin) {
    $recettes = calculRecettesTotales($moulinId, $dateDebut, $dateFin);
    $charges = calculChargesTotales($moulinId, $dateDebut, $dateFin);
    
    $benefice = $recettes - $charges;
    $marge = ($recettes > 0) ? ($benefice / $recettes) * 100 : 0;
    
    return [
        'recettes' => $recettes,
        'charges' => $charges,
        'benefice' => $benefice,
        'marge' => $marge
    ];
}

/**
 * Calcule la production totale d'un moulin sur une période donnée
 * 
 * @param int $moulinId L'identifiant du moulin
 * @param string $dateDebut Date de début au format Y-m-d
 * @param string $dateFin Date de fin au format Y-m-d
 * @return float La quantité totale produite
 */
function calculProductionTotale($moulinId, $dateDebut, $dateFin) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT SUM(quantite) AS total
        FROM productions 
        WHERE moulin_id = ? 
        AND date BETWEEN ? AND ?
    ");
    
    $stmt->execute([$moulinId, $dateDebut, $dateFin]);
    $resultat = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return floatval($resultat['total'] ?? 0);
}

/**
 * Calcule la moyenne journalière de production sur une période donnée
 * Ne considère que les jours où il y a eu de la production
 * 
 * @param int $moulinId L'identifiant du moulin
 * @param string $dateDebut Date de début au format Y-m-d
 * @param string $dateFin Date de fin au format Y-m-d
 * @return float La moyenne journalière de production
 */
function calculMoyenneJournaliereProduction($moulinId, $dateDebut, $dateFin) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT AVG(quantite) AS moyenne
        FROM productions 
        WHERE moulin_id = ? 
        AND date BETWEEN ? AND ?
    ");
    
    $stmt->execute([$moulinId, $dateDebut, $dateFin]);
    $resultat = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return floatval($resultat['moyenne'] ?? 0);
}

/**
 * Calcule la rentabilité par unité de production (bénéfice/kg)
 * 
 * @param int $moulinId L'identifiant du moulin
 * @param string $dateDebut Date de début au format Y-m-d
 * @param string $dateFin Date de fin au format Y-m-d
 * @return float Le bénéfice par unité de production
 */
function calculRentabiliteParUnite($moulinId, $dateDebut, $dateFin) {
    $rentabilite = calculRentabilite($moulinId, $dateDebut, $dateFin);
    $production = calculProductionTotale($moulinId, $dateDebut, $dateFin);
    
    if ($production <= 0) {
        return 0;
    }
    
    return $rentabilite['benefice'] / $production;
}

/**
 * Calcule le retour sur investissement (ROI)
 * 
 * @param int $moulinId L'identifiant du moulin
 * @param string $dateAchat Date d'achat du moulin au format Y-m-d
 * @param string $dateActuelle Date actuelle au format Y-m-d
 * @return array Tableau contenant les informations de ROI
 */
function calculROI($moulinId, $dateAchat, $dateActuelle) {
    global $pdo;
    
    // Récupérer le coût d'achat du moulin
    $stmt = $pdo->prepare("
        SELECT coutAchat 
        FROM moulins 
        WHERE id = ?
    ");
    $stmt->execute([$moulinId]);
    $moulin = $stmt->fetch(PDO::FETCH_ASSOC);
    $coutAchat = floatval($moulin['coutAchat'] ?? 0);
    
    if ($coutAchat <= 0) {
        return [
            'pourcentage' => 0,
            'annualise' => 0,
            'tempsRemboursement' => null
        ];
    }
    
    // Calculer le bénéfice total depuis l'achat
    $rentabilite = calculRentabilite($moulinId, $dateAchat, $dateActuelle);
    $beneficeTotal = $rentabilite['benefice'];
    
    // Calculer le ROI en pourcentage
    $roi = ($beneficeTotal / $coutAchat) * 100;
    
    // Calculer le temps écoulé en années
    $dateAchatObj = new DateTime($dateAchat);
    $dateActuelleObj = new DateTime($dateActuelle);
    $interval = $dateAchatObj->diff($dateActuelleObj);
    $annees = $interval->y + ($interval->m / 12) + ($interval->d / 365.25);
    
    // Calculer le ROI annualisé
    $roiAnnualise = ($annees > 0) ? $roi / $annees : 0;
    
    // Calculer le temps de remboursement (en années)
    $tempsRemboursement = null;
    if ($beneficeTotal > 0) {
        $tempsRemboursement = ($coutAchat / $beneficeTotal) * $annees;
    }
    
    return [
        'pourcentage' => $roi,
        'annualise' => $roiAnnualise,
        'tempsRemboursement' => $tempsRemboursement
    ];
}
