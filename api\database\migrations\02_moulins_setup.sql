-- <PERSON><PERSON><PERSON>
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des moulins
CREATE TABLE IF NOT EXISTS `moulins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(20) NOT NULL,
  `nom` varchar(100) NOT NULL,
  `adresse` varchar(255) DEFAULT NULL,
  `ville` varchar(100) NOT NULL,
  `pays` varchar(100) NOT NULL DEFAULT 'Togo',
  `coordonnees_gps` varchar(100) DEFAULT NULL,
  `date_installation` date DEFAULT NULL,
  `statut` enum('actif','maintenance','hors_service') NOT NULL DEFAULT 'actif',
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY <PERSON>Y (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des types de documents
CREATE TABLE IF NOT EXISTS `document_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(50) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nom` (`nom`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des documents des moulins
CREATE TABLE IF NOT EXISTS `moulin_documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `moulin_id` int(11) NOT NULL,
  `document_type_id` int(11) NOT NULL,
  `titre` varchar(100) NOT NULL,
  `chemin_fichier` varchar(255) NOT NULL,
  `date_ajout` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`document_type_id`) REFERENCES `document_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des événements des moulins
CREATE TABLE IF NOT EXISTS `moulin_evenements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `moulin_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `type` enum('maintenance','panne','reparation','autre') NOT NULL,
  `description` text NOT NULL,
  `date_debut` datetime NOT NULL,
  `date_fin` datetime DEFAULT NULL,
  `cout` decimal(10,2) DEFAULT NULL,
  `statut` enum('en_cours','termine','annule') NOT NULL DEFAULT 'en_cours',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertion des types de documents par défaut
INSERT IGNORE INTO `document_types` (`nom`, `description`) VALUES
('facture', 'Factures d''achat ou de service'),
('photo', 'Photos du moulin'),
('contrat', 'Contrats liés au moulin'),
('maintenance', 'Documents de maintenance'),
('autre', 'Autres types de documents');

-- Insertion d'un moulin de test
INSERT IGNORE INTO `moulins` (`code`, `nom`, `adresse`, `ville`, `pays`, `statut`, `description`, `date_installation`)
VALUES ('M001', 'Moulin Central', 'Rue du Commerce', 'Lomé', 'Togo', 'actif', 'Premier moulin de test', '2025-01-01');
