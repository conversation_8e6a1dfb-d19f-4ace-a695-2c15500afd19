<?php
/**
 * Configuration de la base de données
 * Ce fichier contient les paramètres de connexion à la base de données MySQL/MariaDB
 */

/**
 * Établit une connexion PDO à la base de données
 * @return PDO Instance de connexion PDO
 */
function getConnection() {
    $host = 'localhost';
    $dbname = 'gestion_moulin_db';
    $username = 'root';
    $password = ''; // Par défaut pour XAMPP
    $charset = 'utf8mb4';

    try {
        $dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        return new PDO($dsn, $username, $password, $options);
    } catch (PDOException $e) {
        // Erreur de connexion - Format JSON pour cohérence avec le reste de l'API
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
        exit();
    }
}
